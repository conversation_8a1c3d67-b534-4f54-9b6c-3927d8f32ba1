"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst menuItems = [\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Manajemen Akun',\n        href: '/account-management',\n        adminOnly: true\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: 'Presensi',\n        submenu: [\n            {\n                title: 'Daftar Presensi',\n                href: '/attendance'\n            },\n            {\n                title: 'Isi Presensi',\n                href: '/attendance/record'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: 'Manajemen Karyawan',\n        submenu: [\n            {\n                title: 'Daftar Karyawan',\n                href: '/employee'\n            },\n            {\n                title: 'Presensi Karyawan',\n                href: '/employee/attendance'\n            },\n            {\n                title: 'KPI Karyawan',\n                href: '/employee/kpi'\n            },\n            {\n                title: 'KPI Saya',\n                href: '/employee/mykpi'\n            },\n            {\n                title: 'Penggajian',\n                href: '/employee/salary'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: 'Manajemen Proyek',\n        submenu: [\n            {\n                title: 'Daftar Proyek',\n                href: '/project'\n            },\n            {\n                title: 'Dashboard Proyek',\n                href: '/project/dashboard'\n            },\n            {\n                title: 'Tugas Proyek',\n                href: '/project/task'\n            },\n            {\n                title: 'KPI Proyek',\n                href: '/kpi-project',\n                allowedRoles: [\n                    'Admin',\n                    'Manager',\n                    'Operation'\n                ]\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: 'Manajemen Faktur',\n        href: '/invoice'\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: 'Manajemen Klien',\n        href: '/client'\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [openSections, setOpenSections] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const { isAdmin, hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__.useRBAC)();\n    // Function to get the parent section of a path\n    const getParentSection = (path)=>{\n        for (const item of menuItems){\n            if (item.submenu) {\n                for (const subItem of item.submenu){\n                    if (path === subItem.href) {\n                        return {\n                            parentTitle: item.title,\n                            isSubItemActive: true,\n                            activeSubItem: subItem.href\n                        };\n                    }\n                }\n            } else if (item.href === path) {\n                return {\n                    parentTitle: item.title,\n                    isSubItemActive: false,\n                    activeSubItem: null\n                };\n            }\n        }\n        return null;\n    };\n    // Get current active parent and subitem\n    const activeInfo = getParentSection(pathname);\n    // Initialize open sections based on current path\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeInfo && activeInfo.isSubItemActive) {\n                setOpenSections({\n                    \"Sidebar.useEffect\": (prev)=>{\n                        if (!prev.includes(activeInfo.parentTitle)) {\n                            return [\n                                ...prev,\n                                activeInfo.parentTitle\n                            ];\n                        }\n                        return prev;\n                    }\n                }[\"Sidebar.useEffect\"]);\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        activeInfo\n    ]);\n    const toggleSection = (title, e)=>{\n        e.preventDefault();\n        setOpenSections((prev)=>{\n            if (prev.includes(title)) {\n                return prev.filter((item)=>item !== title);\n            } else {\n                return [\n                    ...prev,\n                    title\n                ];\n            }\n        });\n    };\n    // Filter menu items based on role\n    const filteredMenuItems = menuItems.filter((item)=>{\n        // Hide admin-only items from non-admin users\n        if (item.adminOnly && !isAdmin()) {\n            return false;\n        }\n        // Hide items with role restrictions from users who don't have the required roles\n        if (item.allowedRoles && !hasRole(item.allowedRoles)) {\n            return false;\n        }\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-64 bg-gradient-to-b from-[#AB8B3B] to-[#8B7355] text-white shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex-1 space-y-2 px-3 py-4\",\n            children: filteredMenuItems.map((item)=>{\n                const isParentActive = (activeInfo === null || activeInfo === void 0 ? void 0 : activeInfo.parentTitle) === item.title;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                        open: openSections.includes(item.title),\n                        className: \"transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-lg overflow-hidden', isParentActive && 'bg-white/10 backdrop-blur-sm'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: (e)=>toggleSection(item.title, e),\n                                        className: \"flex w-full cursor-pointer items-center justify-between px-3 py-2.5 text-white hover:bg-white/10 hover:text-white rounded-lg transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('h-4 w-4 transition-transform duration-300', openSections.includes(item.title) ? 'rotate-180' : '')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                    className: \"space-y-1 px-2 py-1\",\n                                    children: item.submenu.filter((subItem)=>{\n                                        // Hide admin-only submenu items from non-admin users\n                                        if (subItem.adminOnly && !isAdmin()) {\n                                            return false;\n                                        }\n                                        // Hide submenu items with role restrictions from users who don't have the required roles\n                                        if (subItem.allowedRoles && !hasRole(subItem.allowedRoles)) {\n                                            return false;\n                                        }\n                                        return true;\n                                    }).map((subItem)=>{\n                                        const isSubItemActive = pathname === subItem.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: subItem.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('block px-9 py-2 text-sm transition-colors rounded-md', isSubItemActive ? 'text-white bg-white/5 font-bold' : 'text-white/90 hover:text-white hover:bg-white/10'),\n                                            children: subItem.title\n                                        }, subItem.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 29\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 17\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-lg overflow-hidden', isParentActive && 'bg-white/10 backdrop-blur-sm'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center gap-2 px-3 py-2.5 rounded-lg transition-colors duration-200', isParentActive ? 'text-white font-bold' : 'text-white hover:text-white hover:bg-white/10'),\n                            children: [\n                                item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 17\n                    }, this)\n                }, item.title, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"O+REYlU6+q6IJn6FB6RXfEpfFSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__.useRBAC\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sidebar.tsx\n"));

/***/ })

});