"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations - White-Gold Theme\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#374151',\n                dark: '#374151'\n            },\n            fill: '#374151'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category2: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        category3: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        },\n        category4: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        category5: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed_below_target: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        completed_on_target: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        completed_above_target: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        }\n    };\n    // Function to get status badge color\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill - White-Gold Theme\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#9CA3AF' : 'bg-gray-500';\n            case 'in_progress':\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n            case 'completed_below_target':\n                return forChart ? '#6B7280' : 'bg-gray-600';\n            case 'completed_on_target':\n                return forChart ? '#AB8B3B' : 'bg-yellow-600';\n            case 'completed_above_target':\n                return forChart ? '#8B7355' : 'bg-yellow-700';\n            default:\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n        }\n    };\n    // Function to get chart fill color for project status - White-Gold Theme\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#9CA3AF'; // Light Gray\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#D4B86A'; // Light Gold\n            case 'completed':\n            case 'selesai':\n                return '#AB8B3B'; // Primary Gold\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#374151'; // Dark Gray\n            default:\n                return '#9CA3AF'; // Light Gray\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories - White-Gold Theme\n    const categoryColors = [\n        '#AB8B3B',\n        '#D4B86A',\n        '#8B7355',\n        '#6B7280',\n        '#9CA3AF'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-4 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 lg:grid-cols-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Dashboard Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-2\",\n                                        children: dashboardData.projects.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: statusChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_12__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_13__.Pie, {\n                                                        data: statusData,\n                                                        dataKey: \"value\",\n                                                        nameKey: \"name\",\n                                                        cx: \"50%\",\n                                                        cy: \"50%\",\n                                                        outerRadius: 40,\n                                                        paddingAngle: 2,\n                                                        children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                fill: getStatusChartColor(entry.name)\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Statistik KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-1\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-[#8B7355] mb-2\",\n                                        children: [\n                                            \"(\",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: kpiChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_12__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_13__.Pie, {\n                                                        data: kpiStatusData,\n                                                        dataKey: \"value\",\n                                                        nameKey: \"name\",\n                                                        innerRadius: 25,\n                                                        outerRadius: 35,\n                                                        paddingAngle: 2,\n                                                        children: kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                fill: entry.fill\n                                                            }, \"cell-\".concat(entry.name), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Statistik Tugas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-1\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-[#8B7355] mb-2\",\n                                        children: [\n                                            \"(\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: picChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_15__.BarChart, {\n                                                data: picData.slice(0, 3),\n                                                layout: \"vertical\",\n                                                margin: {\n                                                    top: 5,\n                                                    right: 10,\n                                                    left: 5,\n                                                    bottom: 5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.XAxis, {\n                                                        type: \"number\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.YAxis, {\n                                                        dataKey: \"name\",\n                                                        type: \"category\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Bar, {\n                                                        dataKey: \"value\",\n                                                        radius: 2,\n                                                        fill: \"#AB8B3B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Tenggat & Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-1\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-2\",\n                                        children: \"30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: categoryChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_15__.BarChart, {\n                                                data: categoryData.slice(0, 3),\n                                                layout: \"vertical\",\n                                                margin: {\n                                                    top: 5,\n                                                    right: 10,\n                                                    left: 5,\n                                                    bottom: 5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.XAxis, {\n                                                        type: \"number\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.YAxis, {\n                                                        dataKey: \"name\",\n                                                        type: \"category\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Bar, {\n                                                        dataKey: \"value\",\n                                                        radius: 2,\n                                                        children: categoryData.slice(0, 3).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                fill: categoryColors[index]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-gradient-to-br from-[#AB8B3B]/5 to-[#D4B86A]/5 border-[#AB8B3B]/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg text-[#AB8B3B]\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 border border-[#AB8B3B]/10 rounded-lg p-3 hover:shadow-sm transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1 text-[#AB8B3B] hover:text-[#8B7355]\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-gradient-to-br from-[#AB8B3B]/5 to-[#D4B86A]/5 border-[#AB8B3B]/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg text-[#AB8B3B]\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 border border-[#AB8B3B]/10 rounded-lg p-3 hover:shadow-sm transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-green-500 text-green-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1 text-[#AB8B3B] hover:text-[#8B7355]\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});