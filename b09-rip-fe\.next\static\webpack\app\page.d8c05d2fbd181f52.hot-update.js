"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingPage.tsx":
/*!************************************************!*\
  !*** ./src/components/landing/LandingPage.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/footer */ \"(app-pages-browser)/./src/components/ui/footer.tsx\");\n/* harmony import */ var _components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/LoginModal */ \"(app-pages-browser)/./src/components/auth/LoginModal.tsx\");\n/* harmony import */ var _components_auth_RegisterModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth/RegisterModal */ \"(app-pages-browser)/./src/components/auth/RegisterModal.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Counter component for animated numbers\nfunction AnimatedCounter(param) {\n    let { end, duration = 2000, suffix = '' } = param;\n    _s();\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const counterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedCounter.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"AnimatedCounter.useEffect\": (param)=>{\n                    let [entry] = param;\n                    if (entry.isIntersecting && !isVisible) {\n                        setIsVisible(true);\n                    }\n                }\n            }[\"AnimatedCounter.useEffect\"], {\n                threshold: 0.1\n            });\n            if (counterRef.current) {\n                observer.observe(counterRef.current);\n            }\n            return ({\n                \"AnimatedCounter.useEffect\": ()=>observer.disconnect()\n            })[\"AnimatedCounter.useEffect\"];\n        }\n    }[\"AnimatedCounter.useEffect\"], [\n        isVisible\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedCounter.useEffect\": ()=>{\n            if (!isVisible) return;\n            let startTime;\n            let animationFrame;\n            const animate = {\n                \"AnimatedCounter.useEffect.animate\": (timestamp)=>{\n                    if (!startTime) startTime = timestamp;\n                    const progress = Math.min((timestamp - startTime) / duration, 1);\n                    setCount(Math.floor(progress * end));\n                    if (progress < 1) {\n                        animationFrame = requestAnimationFrame(animate);\n                    }\n                }\n            }[\"AnimatedCounter.useEffect.animate\"];\n            animationFrame = requestAnimationFrame(animate);\n            return ({\n                \"AnimatedCounter.useEffect\": ()=>{\n                    if (animationFrame) {\n                        cancelAnimationFrame(animationFrame);\n                    }\n                }\n            })[\"AnimatedCounter.useEffect\"];\n        }\n    }[\"AnimatedCounter.useEffect\"], [\n        isVisible,\n        end,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: counterRef,\n        className: \"text-4xl font-bold text-[#B78F38] mb-3 group-hover:scale-110 transition-transform\",\n        children: [\n            count,\n            suffix\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(AnimatedCounter, \"snMCoIaYcVv/UrRTcpdar0fy6c8=\");\n_c = AnimatedCounter;\n// Landing Page Navbar component\nfunction LandingNavbar(param) {\n    let { onLoginClick, onRegisterClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 w-full border-b bg-white/95 backdrop-blur-sm shadow-sm py-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center h-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                        href: \"/\",\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            src: \"/logo-navbar.svg\",\n                            alt: \"Kasuat\",\n                            width: 160,\n                            height: 40,\n                            className: \"h-8 w-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            className: \"bg-[#B78F38] hover:bg-[#A67D32] text-white px-6 py-2\",\n                            onClick: onLoginClick,\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            className: \"text-gray-700 hover:text-[#B78F38] hover:bg-[#B78F38]/10 px-4 py-2\",\n                            onClick: onRegisterClick,\n                            children: \"Daftar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LandingNavbar;\nfunction LandingPage() {\n    _s1();\n    const [isLoginOpen, setIsLoginOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRegisterOpen, setIsRegisterOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LandingNavbar, {\n                onLoginClick: ()=>setIsLoginOpen(true),\n                onRegisterClick: ()=>setIsRegisterOpen(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white min-h-screen flex items-start pb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-[#B78F38]/5 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-10 w-20 h-20 bg-[#B78F38]/10 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-40 right-20 w-16 h-16 bg-[#B78F38]/20 rounded-full animate-bounce delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-40 left-20 w-12 h-12 bg-[#B78F38]/15 rounded-full animate-pulse delay-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-4 lg:py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 lg:gap-16 xl:gap-20 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left space-y-6 lg:space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 lg:space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight animate-slide-up\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-[#B78F38] font-bold\",\n                                                            children: \"Kasuat\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700 text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold\",\n                                                            children: \"Information Systems\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg sm:text-xl lg:text-2xl text-gray-600 leading-relaxed animate-slide-up delay-200 max-w-2xl\",\n                                                    children: \"Portal terpadu tim internal dan klien Kasuat untuk meningkatkan efisiensi dalam satu platform terintegrasi.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4 pt-2 lg:pt-4 animate-slide-up delay-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    className: \"border-2 border-[#B78F38] text-[#B78F38] hover:bg-[#B78F38] hover:text-white px-8 lg:px-10 py-3 lg:py-4 text-base lg:text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105\",\n                                                    onClick: ()=>setIsRegisterOpen(true),\n                                                    children: \"Daftar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    className: \"bg-[#B78F38] hover:bg-[#A67D32] text-white px-8 lg:px-10 py-3 lg:py-4 text-base lg:text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105\",\n                                                    onClick: ()=>setIsLoginOpen(true),\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative animate-fade-in delay-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                src: \"/assets/illustrations/landing-1.svg\",\n                                                alt: \"Office Illustration\",\n                                                width: 500,\n                                                height: 500,\n                                                className: \"w-full h-auto max-w-lg\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 -right-4 w-16 h-16 bg-[#B78F38]/20 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-4 -left-4 w-12 h-12 bg-[#B78F38]/30 rounded-full animate-bounce\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-[#B78F38] to-[#A67D32] relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 w-32 h-32 border border-white/20 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 left-1/4 w-40 h-40 border border-white/20 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12 lg:mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                        children: \"Fitur Utama\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg sm:text-xl text-white/90 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Akses lengkap untuk tim internal dan klien dalam mengelola proyek konsultasi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-[#B78F38]\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Manajemen Proyek\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: \"Kelola proyek konsultasi klien dengan tracking progress secara real-time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-[#B78F38]\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Manajemen SDM\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: \"Sistem absensi, evaluasi KPI, dan laporan mingguan untuk tim internal Kasuat.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-[#B78F38]\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Dashboard & Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: \"Monitoring performa proyek, analisis produktivitas tim, dan laporan eksekutif.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-[#B78F38]\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Manajemen Keuangan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: \"Tracking invoice klien, budget proyek, dan laporan keuangan untuk operasional konsultasi.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-[#B78F38]\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Database Klien\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: \"Manajemen data klien dan riwayat proyek untuk layanan konsultasi yang optimal.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-[#B78F38]\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Keamanan & Akses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: \"Kontrol akses untuk data sensitif perusahaan dan informasi klien.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 sm:py-20 lg:py-24 bg-white relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-10 w-64 h-64 bg-[#B78F38] rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 right-10 w-48 h-48 bg-[#B78F38] rounded-full animate-pulse delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-slide-up\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block bg-[#B78F38]/10 text-[#B78F38] px-4 py-2 rounded-full text-sm font-medium mb-6\",\n                                            children: \"PT Halaman Tumbuh Bersama\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-8\",\n                                            children: [\n                                                \"Tentang \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#B78F38]\",\n                                                    children: \"Kasuat\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                            children: \"Kasuat adalah perusahaan konsultasi terpercaya yang telah melayani berbagai klien dengan solusi bisnis yang inovatif dan berkelanjutan. Kami memiliki fokus dalam memberikan layanan konsultasi strategis untuk berbagai industri.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 mb-12 leading-relaxed\",\n                                            children: \"Dengan tim profesional berpengalaman dan pendekatan yang terstruktur, kami berkomitmen memberikan hasil terbaik untuk setiap proyek yang dipercayakan kepada kami.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                            end: 25,\n                                                            suffix: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-600 font-medium\",\n                                                            children: \"Proyek Selesai\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                            end: 30,\n                                                            suffix: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-600 font-medium\",\n                                                            children: \"Klien Aktif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                            end: 95,\n                                                            suffix: \"%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-600 font-medium\",\n                                                            children: \"Tingkat Kepuasan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        src: \"/assets/illustrations/landing-2.svg\",\n                                        alt: \"Office Illustration\",\n                                        width: 500,\n                                        height: 500,\n                                        className: \"w-full h-auto max-w-lg\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-[#B78F38] to-[#A67D32] relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 right-20 w-96 h-96 border-2 border-white/20 rounded-full animate-spin-slow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 left-20 w-64 h-64 border border-white/20 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12 lg:mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                        children: \"Keunggulan Sistem\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg sm:text-xl text-white/90 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Menyediakan solusi lengkap untuk kebutuhan operasional perusahaan\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-10 h-10 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-3\",\n                                                children: \"Efisiensi Tinggi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"Otomatisasi proses bisnis untuk produktivitas maksimal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-10 h-10 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-3\",\n                                                children: \"Terintegrasi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"Semua modul terhubung dalam satu platform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-10 h-10 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-3\",\n                                                children: \"Aman\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"Keamanan data tingkat enterprise\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-10 h-10 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-3\",\n                                                children: \"Real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"Monitoring dan laporan secara langsung\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 sm:py-20 lg:py-24 bg-gray-100 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-1/4 w-32 h-32 bg-[#B78F38] rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 right-1/4 w-24 h-24 bg-[#B78F38] rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-5xl mx-auto text-center px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 lg:mb-8\",\n                                children: \"Mulai Akses Sekarang!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg sm:text-xl text-gray-600 mb-8 lg:mb-12 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Kelola proyek konsultasi dan informasi penting dalam satu platform.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-[#B78F38] hover:bg-[#A67D32] text-white px-8 lg:px-12 py-4 lg:py-6 text-lg lg:text-xl font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105\",\n                                        onClick: ()=>setIsLoginOpen(true),\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"border-2 border-[#B78F38] text-[#B78F38] hover:bg-[#B78F38] hover:text-white px-8 lg:px-12 py-4 lg:py-6 text-lg lg:text-xl font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105\",\n                                        onClick: ()=>setIsRegisterOpen(true),\n                                        children: \"Daftar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: isLoginOpen,\n                onClose: ()=>setIsLoginOpen(false),\n                onOpenRegister: ()=>{\n                    setIsLoginOpen(false);\n                    setIsRegisterOpen(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RegisterModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isRegisterOpen,\n                onClose: ()=>setIsRegisterOpen(false),\n                onOpenLogin: ()=>{\n                    setIsRegisterOpen(false);\n                    setIsLoginOpen(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\landing\\\\LandingPage.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s1(LandingPage, \"g2T/yTQJYb9BK0YQl4zcqjnRytk=\");\n_c2 = LandingPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AnimatedCounter\");\n$RefreshReg$(_c1, \"LandingNavbar\");\n$RefreshReg$(_c2, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingPage.tsx\n"));

/***/ })

});