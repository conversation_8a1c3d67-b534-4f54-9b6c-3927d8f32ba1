"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Warna tema gold & abu yang harmonis (bukan random)\n    const GOLD = \"#AB8B3B\";\n    const WHITE = \"#F4F4F4\";\n    const DARK_GREY = \"#232323\";\n    const LIGHT_GREY = \"#B4B4B4\";\n    const MEDIUM_GREY = \"#888888\";\n    // Palet status chart (pie)\n    const statusPiePalette = [\n        GOLD,\n        DARK_GREY,\n        MEDIUM_GREY,\n        LIGHT_GREY // Cancelled / Dibatalkan\n    ];\n    // Pie chart color mapping\n    function getStatusChartColor(status) {\n        switch(status.toLowerCase()){\n            case \"not started\":\n            case \"not_started\":\n            case \"belum dimulai\":\n                return statusPiePalette[0]; // GOLD\n            case \"in progress\":\n            case \"in_progress\":\n            case \"dalam proses\":\n                return statusPiePalette[1]; // DARK GREY\n            case \"completed\":\n            case \"selesai\":\n                return statusPiePalette[2]; // MEDIUM GREY\n            case \"cancelled\":\n            case \"dibatalkan\":\n                return statusPiePalette[3]; // LIGHT GREY\n            default:\n                return WHITE; // fallback\n        }\n    }\n    // Chart configurations (non-pie)\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: GOLD,\n                dark: GOLD\n            },\n            fill: GOLD\n        },\n        category2: {\n            theme: {\n                light: DARK_GREY,\n                dark: DARK_GREY\n            },\n            fill: DARK_GREY\n        },\n        category3: {\n            theme: {\n                light: MEDIUM_GREY,\n                dark: MEDIUM_GREY\n            },\n            fill: MEDIUM_GREY\n        },\n        category4: {\n            theme: {\n                light: LIGHT_GREY,\n                dark: LIGHT_GREY\n            },\n            fill: LIGHT_GREY\n        },\n        category5: {\n            theme: {\n                light: WHITE,\n                dark: WHITE\n            },\n            fill: WHITE\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: GOLD,\n                dark: GOLD\n            },\n            fill: GOLD\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: LIGHT_GREY,\n                dark: LIGHT_GREY\n            },\n            fill: LIGHT_GREY\n        },\n        in_progress: {\n            theme: {\n                light: MEDIUM_GREY,\n                dark: MEDIUM_GREY\n            },\n            fill: MEDIUM_GREY\n        },\n        completed_below_target: {\n            theme: {\n                light: GOLD,\n                dark: GOLD\n            },\n            fill: GOLD\n        },\n        completed_on_target: {\n            theme: {\n                light: DARK_GREY,\n                dark: DARK_GREY\n            },\n            fill: DARK_GREY\n        },\n        completed_above_target: {\n            theme: {\n                light: WHITE,\n                dark: WHITE\n            },\n            fill: WHITE\n        }\n    };\n    // Status badge color\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-[#F4F4F4] text-[#232323] border border-[#B4B4B4]';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-[#232323] text-white';\n            case 'completed':\n                return 'bg-[#888888] text-white';\n            case 'cancelled':\n                return 'bg-[#B4B4B4] text-[#232323]';\n            default:\n                return 'bg-[#F4F4F4] text-[#232323] border border-[#B4B4B4]';\n        }\n    };\n    // KPI status color/fill\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? LIGHT_GREY : 'bg-[#B4B4B4]';\n            case 'in_progress':\n                return forChart ? MEDIUM_GREY : 'bg-[#888888]';\n            case 'completed_below_target':\n                return forChart ? GOLD : 'bg-[#AB8B3B]';\n            case 'completed_on_target':\n                return forChart ? DARK_GREY : 'bg-[#232323]';\n            case 'completed_above_target':\n                return forChart ? WHITE : 'bg-[#F4F4F4] text-[#232323] border';\n            default:\n                return forChart ? DARK_GREY : 'bg-[#232323]';\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this);\n    }\n    // Pie chart data: gunakan warna harmonis\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: getStatusChartColor(key)\n        };\n    });\n    // Category chart colors\n    const categoryColors = [\n        GOLD,\n        DARK_GREY,\n        MEDIUM_GREY,\n        LIGHT_GREY,\n        WHITE\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: GOLD\n        }));\n    // KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Only upcoming deadlines in next 30 days\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Selesai: \",\n                                            dashboardData.tasks.completed,\n                                            \" (\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Proyek dengan tenggat waktu dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                    data: statusData,\n                                                    dataKey: \"value\",\n                                                    nameKey: \"name\",\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 80,\n                                                    paddingAngle: 2,\n                                                    label: true,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegend, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegendContent, {\n                                                        nameKey: \"name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 30,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    width: 120\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    children: categoryData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 50,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    cursor: false,\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    fill: GOLD,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 8,\n                                                            className: \"fill-[--color-label]\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 8,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex-1 pb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                    config: kpiChartConfig,\n                                    className: \"mx-auto aspect-square max-h-[250px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                cursor: false,\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                    hideLabel: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 28\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                data: kpiStatusData,\n                                                dataKey: \"value\",\n                                                nameKey: \"name\",\n                                                innerRadius: 60,\n                                                outerRadius: 80,\n                                                paddingAngle: 2,\n                                                children: [\n                                                    kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(entry.name), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                        content: (param)=>{\n                                                            let { viewBox } = param;\n                                                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                    x: viewBox.cx,\n                                                                    y: viewBox.cy,\n                                                                    textAnchor: \"middle\",\n                                                                    dominantBaseline: \"middle\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            className: \"fill-foreground text-3xl font-bold\",\n                                                                            children: [\n                                                                                totalKpiAchievement,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: (viewBox.cy || 0) + 24,\n                                                                            className: \"fill-muted-foreground text-sm\",\n                                                                            children: \"Pencapaian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-col gap-2 text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 font-medium leading-none justify-center\",\n                                        children: dashboardData.kpis.achievement_percentage >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian melebihi target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : dashboardData.kpis.achievement_percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian mendekati target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian perlu ditingkatkan\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-green-500 text-green-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 571,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});