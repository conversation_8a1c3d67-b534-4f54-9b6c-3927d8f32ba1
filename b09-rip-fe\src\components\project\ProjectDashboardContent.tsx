'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  BarChart2,
  Calendar,
  CheckCircle,
  FileText,
  Target,
  AlertCircle,
  Plus,
  CheckSquare,
  CalendarDays,
  Eye,
  Scroll,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/utils/format';
import { formatDate } from '@/lib/utils/date';
import useProjectDashboard from '@/hooks/useProjectDashboard';
import { Project } from '@/types/project';
import { projectApi } from '@/lib/api/project';
import { useRB<PERSON> } from '@/hooks/useRBAC';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

interface ProjectDashboardContentProps {
  id: string;
}

export function ProjectDashboardContent({ id }: ProjectDashboardContentProps) {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Operation', 'Manager']);
  const canDelete = hasRole(['Manager']);
  const { dashboardData, loading, error, refreshDashboard } =
    useProjectDashboard(id);
  const [project, setProject] = useState<Project | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Fetch full project data to get project_charter_id
  const fetchProject = useCallback(async () => {
    if (!id) return;

    try {
      const response = await projectApi.getProjectById(id);
      if (response.success && response.data) {
        setProject(response.data);
      } else {
        console.error(`Failed to fetch project: ${response.message}`);
      }
    } catch (error) {
      console.error('Error fetching project:', error);
    }
  }, [id]);

  useEffect(() => {
    fetchProject();
  }, [fetchProject]);

  // Handle project deletion
  const handleDelete = async () => {
    if (!canDelete) {
      toast.error('Anda tidak memiliki izin untuk menghapus proyek');
      setDeleteDialogOpen(false);
      return;
    }

    try {
      setDeleteLoading(true);
      const response = await projectApi.deleteProject(id);
      if (response.success) {
        toast.success('Proyek berhasil dihapus');
        router.push('/project');
      } else {
        toast.error(`Gagal menghapus proyek: ${response.message}`);
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      toast.error('Terjadi kesalahan saat menghapus proyek');
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  // Function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-200 text-gray-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  // Function to get KPI status badge color
  const getKpiStatusColor = (status: string) => {
    switch (status) {
      case 'not_started':
        return 'bg-gray-200 text-gray-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed_below_target':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed_on_target':
        return 'bg-green-100 text-green-800';
      case 'completed_above_target':
        return 'bg-emerald-100 text-emerald-800';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  // Function to get task status badge color
  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'not_completed':
        return 'bg-red-100 text-red-800';
      case 'on_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  // Function to format KPI status for display
  const formatKpiStatus = (status: string) => {
    switch (status) {
      case 'not_started':
        return 'Belum Dimulai';
      case 'in_progress':
        return 'Dalam Proses';
      case 'completed_below_target':
        return 'Selesai Di Bawah Target';
      case 'completed_on_target':
        return 'Selesai Sesuai Target';
      case 'completed_above_target':
        return 'Selesai Di Atas Target';
      default:
        return status;
    }
  };

  // Function to format task status for display
  const formatTaskStatus = (status: string) => {
    switch (status) {
      case 'not_completed':
        return 'Belum Selesai';
      case 'on_progress':
        return 'Dalam Proses';
      case 'completed':
        return 'Selesai';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push(`/project`)} />
          <PageTitle title="Dashboard Proyek" />
        </div>
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-64" />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            </CardContent>
          </Card>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push('/project')} />
          <PageTitle title="Dashboard Proyek" />
        </div>
        <Card className="bg-red-50 border-red-200">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>Terjadi kesalahan saat memuat data dashboard.</p>
            </div>
            <p className="mt-2 text-sm text-red-600">
              {error || 'Data tidak tersedia'}
            </p>
            <Button
              onClick={refreshDashboard}
              variant="outline"
              className="mt-4"
            >
              Coba Lagi
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Perhitungan aman untuk timeline
  const elapsedPercent =
    dashboardData.days_total > 0
      ? (dashboardData.days_elapsed / dashboardData.days_total) * 100
      : 0;
  const safePercent = Math.min(elapsedPercent, 100);

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push(`/project`)} />
          <PageTitle title="Dashboard Proyek" />
        </div>
        <div className="flex gap-3">
          {project && (
            <>
              {canEdit &&
                (!project.project_charter_id ||
                  project.project_charter_id ===
                    'TODO-project-charter-implementation') && (
                  <Button
                    onClick={() =>
                      router.push(`/project/${project.id}/charter/create`)
                    }
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Tambah Project Charter
                  </Button>
                )}
              {project.project_charter_id &&
                project.project_charter_id !==
                  'TODO-project-charter-implementation' && (
                  <Button
                    onClick={() =>
                      router.push(`/project/${project.id}/charter`)
                    }
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Scroll className="h-4 w-4" />
                    Project Charter
                  </Button>
                )}
            </>
          )}

          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => router.push(`/project/${id}/detail`)}
          >
            <Eye className="h-4 w-4" />
            Detail Proyek
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => router.push(`/project/${id}/gantt`)}
          >
            <BarChart2 className="h-4 w-4" />
            Gantt Chart
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => router.push(`/project/${id}/weekly-log`)}
          >
            <CalendarDays className="h-4 w-4" />
            Weekly Log
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() =>
              router.push(`/kpi-project/with-details?projectId=${id}`)
            }
          >
            <Target className="h-4 w-4" />
            Lihat KPI
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => router.push(`/project/task?projectId=${id}`)}
          >
            <CheckSquare className="h-4 w-4" />
            Tugas Proyek
          </Button>

          {canDelete && (
            <Button
              variant="destructive"
              onClick={() => setDeleteDialogOpen(true)}
            >
              Hapus
            </Button>
          )}
        </div>
      </div>

      {/* Project Header Section */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h2 className="text-2xl font-bold">
                {dashboardData.project_name}
              </h2>
              <p className="text-gray-500">{dashboardData.organization_name}</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(dashboardData.status_project)}>
                {dashboardData.status_project}
              </Badge>
              <Badge variant="outline">{dashboardData.project_category}</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <h3 className="font-medium text-gray-500 mb-2">Tujuan Proyek</h3>
              <p className="text-sm">{dashboardData.objectives}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 mb-2">Anggaran</h3>
              <p className="text-xl font-semibold">
                {formatCurrency(parseInt(dashboardData.budget_project))}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress Overview Section */}
      <div className="grid gap-6 md:grid-cols-2 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart2 className="h-5 w-5" />
              <span>Progres Proyek</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">
                  Penyelesaian: {dashboardData.progress_percentage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${dashboardData.progress_percentage}%` }}
                ></div>
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4 mt-6">
              <div className="text-center">
                <p className="text-sm text-gray-500">Hari Berlalu</p>
                <p className="text-xl font-bold">
                  {dashboardData.days_elapsed}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Hari Tersisa</p>
                <p className="text-xl font-bold">
                  {dashboardData.days_remaining}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Total Hari</p>
                <p className="text-xl font-bold">{dashboardData.days_total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <span>Timeline Proyek</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Tanggal Mulai</p>
                  <p className="font-medium">
                    {formatDate(dashboardData.start_project)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Tanggal Selesai</p>
                  <p className="font-medium">
                    {formatDate(dashboardData.end_project)}
                  </p>
                </div>
              </div>
              {/* PERBAIKAN TIMELINE */}
              <div className="relative pt-4 overflow-hidden" style={{ minHeight: 20 }}>
                <div className="w-full bg-gray-200 h-1 absolute top-0 left-0 rounded-full"></div>
                <div
                  className="bg-amber-400 h-1 absolute top-0 left-0 rounded-full"
                  style={{
                    width: `${safePercent}%`,
                  }}
                ></div>
                {/* DOT: Jangan pernah lebih dari 100% */}
                <div
                  className="w-3 h-3 bg-amber-600 rounded-full absolute top-[-4px] border-2 border-white shadow"
                  style={{
                    left: `${safePercent}%`,
                    transform: 'translateX(-50%)',
                    transition: 'left 0.3s',
                    zIndex: 1,
                  }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KPI, Tasks, and Weekly Logs Sections */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* KPI Status Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              <span>Status KPI ({dashboardData.kpi_count})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <Badge className={getKpiStatusColor(dashboardData.kpi_status)}>
                {formatKpiStatus(dashboardData.kpi_status)}
              </Badge>
            </div>
            {dashboardData.kpis.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.kpis.map((kpi) => (
                  <div key={kpi.id} className="border rounded-lg p-3">
                    <p className="font-medium">{kpi.description}</p>
                    <p className="text-sm text-gray-600 mt-1">
                      Target: {kpi.target}
                    </p>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-xs text-gray-500">
                        {kpi.period}
                      </span>
                      <Badge className={getKpiStatusColor(kpi.status)}>
                        {formatKpiStatus(kpi.status)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">
                Tidak ada KPI yang tersedia
              </p>
            )}
            <div className="mt-4">
              <Button
                variant="outline"
                className="w-full"
                onClick={() =>
                  router.push(`/kpi-project/with-details?projectId=${id}`)
                }
              >
                Lihat Semua KPI
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Task Management Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              <span>Tugas ({dashboardData.tasks_total})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-2 mb-4">
              <div className="bg-green-50 rounded-lg p-2 text-center">
                <p className="text-xs text-gray-600">Selesai</p>
                <p className="font-bold text-green-700">
                  {dashboardData.tasks_completed}
                </p>
              </div>
              <div className="bg-blue-50 rounded-lg p-2 text-center">
                <p className="text-xs text-gray-600">Proses</p>
                <p className="font-bold text-blue-700">
                  {dashboardData.tasks_in_progress}
                </p>
              </div>
              <div className="bg-red-50 rounded-lg p-2 text-center">
                <p className="text-xs text-gray-600">Belum</p>
                <p className="font-bold text-red-700">
                  {dashboardData.tasks_not_started}
                </p>
              </div>
            </div>
            {dashboardData.recent_tasks.length > 0 ? (
              <div className="space-y-3">
                {dashboardData.recent_tasks.map((task) => (
                  <div key={task.id} className="border rounded-lg p-3">
                    <p className="font-medium text-sm">{task.description}</p>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-xs text-gray-500">
                        {task.employee_name || 'Tidak ada assignee'}
                      </span>
                      <Badge
                        className={getTaskStatusColor(task.completion_status)}
                      >
                        {formatTaskStatus(task.completion_status)}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Tenggat: {formatDate(task.due_date)}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">
                Tidak ada tugas yang tersedia
              </p>
            )}
            <div className="mt-4">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push(`/project/task?projectId=${id}`)}
              >
                Lihat Semua Tugas
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Weekly Logs Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              <span>Log Mingguan ({dashboardData.weekly_logs_count})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {dashboardData.recent_weekly_logs.length > 0 ? (
              <div className="space-y-3">
                {dashboardData.recent_weekly_logs.slice(0, 5).map((log) => (
                  <div key={log.id} className="border rounded-lg p-3">
                    <div className="flex justify-between items-center">
                      <p className="font-medium">Minggu #{log.week_number}</p>
                      <Badge variant="outline" className="text-xs">
                        {log.notes_count} catatan
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(log.week_start_date)} -{' '}
                      {formatDate(log.week_end_date)}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">
                Tidak ada log mingguan yang tersedia
              </p>
            )}
            <div className="mt-4">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push(`/project/${id}/weekly-log`)}
              >
                Lihat Semua Log
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Proyek</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteLoading}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteLoading}
            >
              {deleteLoading ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
