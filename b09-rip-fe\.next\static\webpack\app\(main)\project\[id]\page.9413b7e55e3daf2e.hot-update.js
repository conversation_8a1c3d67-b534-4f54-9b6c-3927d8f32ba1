"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx":
/*!************************************************************!*\
  !*** ./src/components/project/ProjectDashboardContent.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDashboardContent: () => (/* binding */ ProjectDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/scroll.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/BackButton */ \"(app-pages-browser)/./src/components/ui/BackButton.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/format */ \"(app-pages-browser)/./src/lib/utils/format.ts\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useProjectDashboard */ \"(app-pages-browser)/./src/hooks/useProjectDashboard.ts\");\n/* harmony import */ var _lib_api_project__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api/project */ \"(app-pages-browser)/./src/lib/api/project.ts\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Enhanced Gold/White palette\nconst palette = {\n    bgMain: 'bg-[#fffbea]',\n    bgCard: 'bg-white',\n    bgCardSecondary: 'bg-[#fefcf3]',\n    gold: 'text-[#dfb14a]',\n    goldDark: 'text-[#b8941f]',\n    goldLight: 'text-[#f4d76a]',\n    goldBg: 'bg-[#fff2ce]',\n    goldAccent: 'bg-[#f7e3a1]',\n    goldGradient: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    goldGradientDark: 'bg-gradient-to-r from-[#dfb14a] to-[#b8941f]',\n    border: 'border-[#ecd9a0]',\n    borderLight: 'border-[#f5e8b8]',\n    progress: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    progressTrack: 'bg-[#fff8e1]',\n    green: 'text-emerald-700 bg-emerald-100',\n    blue: 'text-blue-700 bg-blue-100',\n    red: 'text-red-700 bg-red-100',\n    gray: 'text-gray-600 bg-gray-50',\n    grayDark: 'text-gray-700',\n    grayLight: 'text-gray-500',\n    shadow: 'shadow-sm shadow-[#f5e8b8]/20'\n};\nfunction ProjectDashboardContent(param) {\n    let { id } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC)();\n    const canEdit = hasRole([\n        'Operation',\n        'Manager'\n    ]);\n    const canDelete = hasRole([\n        'Manager'\n    ]);\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(id);\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteLoading, setDeleteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch full project data to get project_charter_id\n    const fetchProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProjectDashboardContent.useCallback[fetchProject]\": async ()=>{\n            if (!id) return;\n            try {\n                const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.getProjectById(id);\n                if (response.success && response.data) {\n                    setProject(response.data);\n                } else {\n                    console.error(\"Failed to fetch project: \".concat(response.message));\n                }\n            } catch (error) {\n                console.error('Error fetching project:', error);\n            }\n        }\n    }[\"ProjectDashboardContent.useCallback[fetchProject]\"], [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDashboardContent.useEffect\": ()=>{\n            fetchProject();\n        }\n    }[\"ProjectDashboardContent.useEffect\"], [\n        fetchProject\n    ]);\n    // Handle project deletion\n    const handleDelete = async ()=>{\n        if (!canDelete) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Anda tidak memiliki izin untuk menghapus proyek');\n            setDeleteDialogOpen(false);\n            return;\n        }\n        try {\n            setDeleteLoading(true);\n            const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.deleteProject(id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Proyek berhasil dihapus');\n                router.push('/project');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Gagal menghapus proyek: \".concat(response.message));\n            }\n        } catch (error) {\n            console.error('Error deleting project:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Terjadi kesalahan saat menghapus proyek');\n        } finally{\n            setDeleteLoading(false);\n            setDeleteDialogOpen(false);\n        }\n    };\n    // --- Status helpers\n    const statusColors = {\n        'Not Started': palette.gray,\n        'In Progress': palette.blue,\n        'Completed': palette.green,\n        'Cancelled': palette.red\n    };\n    const getStatusColor = (status)=>statusColors[status] || palette.gray;\n    const kpiColors = {\n        'not_started': palette.gray,\n        'in_progress': palette.blue,\n        'completed_below_target': 'bg-yellow-100 text-yellow-800',\n        'completed_on_target': palette.green,\n        'completed_above_target': 'bg-[#e7f8e5] text-[#107c41]'\n    };\n    const getKpiStatusColor = (status)=>kpiColors[status] || palette.gray;\n    const taskColors = {\n        'not_completed': palette.red,\n        'on_progress': palette.blue,\n        'completed': palette.green\n    };\n    const getTaskStatusColor = (status)=>taskColors[status] || palette.gray;\n    // Format status\n    const formatKpiStatus = (status)=>({\n            'not_started': 'Belum Dimulai',\n            'in_progress': 'Dalam Proses',\n            'completed_below_target': 'Selesai Di Bawah Target',\n            'completed_on_target': 'Selesai Sesuai Target',\n            'completed_above_target': 'Selesai Di Atas Target'\n        })[status] || status;\n    const formatTaskStatus = (status)=>({\n            'not_completed': 'Belum Selesai',\n            'on_progress': 'Dalam Proses',\n            'completed': 'Selesai'\n        })[status] || status;\n    // Calculate task completion percent\n    let taskPercent = 0;\n    let kpiPercent = 0;\n    if (dashboardData && dashboardData.tasks_total > 0) {\n        taskPercent = Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100);\n    }\n    if (dashboardData && dashboardData.kpi_count > 0 && dashboardData.kpis) {\n        const completedKpis = dashboardData.kpis.filter((kpi)=>kpi.status === 'completed_on_target' || kpi.status === 'completed_above_target').length;\n        kpiPercent = Math.round(completedKpis / dashboardData.kpi_count * 100);\n    }\n    // --- Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push(\"/project\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-2 lg:grid-cols-4 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push('/project')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border \".concat(palette.border, \" \").concat(palette.goldBg, \" \").concat(palette.shadow),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-3\",\n                                children: \"Coba Lagi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                                onClick: ()=>router.push(\"/project\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                                title: \"Dashboard Proyek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 flex-wrap\",\n                        children: [\n                            project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canEdit && (!project.project_charter_id || project.project_charter_id === 'TODO-project-charter-implementation') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter/create\")),\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.goldBg, \" border \").concat(palette.border, \" \").concat(palette.gold, \" hover:\").concat(palette.goldAccent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this),\n                                    project.project_charter_id && project.project_charter_id !== 'TODO-project-charter-implementation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter\")),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.gold, \" border-[#dfb14a]\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/detail\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Detail\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/gantt\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Gantt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Log\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"KPI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Tugas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                size: \"sm\",\n                                onClick: ()=>setDeleteDialogOpen(true),\n                                children: \"Hapus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-4 gap-2 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"lg:col-span-2 \".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-bold mb-1\",\n                                            children: dashboardData.project_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"\".concat(palette.gold, \" text-sm mb-2\"),\n                                            children: dashboardData.organization_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(dashboardData.status_project),\n                                                    size: \"sm\",\n                                                    children: dashboardData.status_project\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: dashboardData.project_category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs \".concat(palette.grayLight),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Tujuan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" \",\n                                                        dashboardData.objectives\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Anggaran:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold \".concat(palette.goldDark, \" ml-1\"),\n                                                            children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs \".concat(palette.grayLight, \" mb-1\"),\n                                        children: \"Progres Proyek\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center justify-center h-16 w-16 mx-auto mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute\",\n                                                width: \"64\",\n                                                height: \"64\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"32\",\n                                                        cy: \"32\",\n                                                        r: \"26\",\n                                                        stroke: \"#fff8e1\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"32\",\n                                                        cy: \"32\",\n                                                        r: \"26\",\n                                                        stroke: \"#dfb14a\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\",\n                                                        strokeDasharray: 2 * Math.PI * 26,\n                                                        strokeDashoffset: 2 * Math.PI * 26 * (1 - dashboardData.progress_percentage / 100),\n                                                        strokeLinecap: \"round\",\n                                                        style: {\n                                                            transition: 'stroke-dashoffset 0.5s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\".concat(palette.goldDark, \" absolute font-bold text-lg\"),\n                                                children: [\n                                                    dashboardData.progress_percentage,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_elapsed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Berlalu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_remaining\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Tersisa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs \".concat(palette.grayLight, \" mb-2\"),\n                                        children: \"Timeline Proyek\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(palette.goldDark),\n                                                        children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.start_project)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Mulai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(palette.goldDark),\n                                                        children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.end_project)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-2 relative rounded-full\",\n                                        style: {\n                                            background: '#fff8e1'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute h-2 rounded-full\",\n                                                style: {\n                                                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',\n                                                    width: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 w-3 h-3 rounded-full border border-white\",\n                                                style: {\n                                                    background: '#dfb14a',\n                                                    left: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\"),\n                                                    transform: 'translate(-50%,-25%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-4 md:grid-cols-2 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold \".concat(palette.goldDark),\n                                                children: dashboardData.kpi_count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs \".concat(palette.grayLight),\n                                                children: \"Total KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center justify-center h-14 w-14 mx-auto mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute\",\n                                                width: \"56\",\n                                                height: \"56\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"28\",\n                                                        cy: \"28\",\n                                                        r: \"22\",\n                                                        stroke: \"#fff8e1\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"28\",\n                                                        cy: \"28\",\n                                                        r: \"22\",\n                                                        stroke: \"#dfb14a\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\",\n                                                        strokeDasharray: 2 * Math.PI * 22,\n                                                        strokeDashoffset: 2 * Math.PI * 22 * (1 - kpiPercent / 100),\n                                                        strokeLinecap: \"round\",\n                                                        style: {\n                                                            transition: 'stroke-dashoffset 0.5s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute text-sm font-bold \".concat(palette.goldDark),\n                                                children: [\n                                                    kpiPercent,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs \".concat(palette.grayLight, \" mb-2\"),\n                                        children: \"Pencapaian\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        className: getKpiStatusColor(dashboardData.kpi_status),\n                                        size: \"sm\",\n                                        children: formatKpiStatus(dashboardData.kpi_status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full mt-2 text-xs\",\n                                        onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                        children: \"Detail KPI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold \".concat(palette.goldDark),\n                                                children: dashboardData.tasks_total\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs \".concat(palette.grayLight),\n                                                children: \"Total Tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-3 rounded-full mb-2 relative\",\n                                        style: {\n                                            background: '#fff8e1'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 rounded-full\",\n                                                style: {\n                                                    width: \"\".concat(taskPercent, \"%\"),\n                                                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute right-1 top-0 text-xs font-bold \".concat(palette.goldDark, \" leading-3\"),\n                                                children: [\n                                                    taskPercent,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs \".concat(palette.grayLight, \" mb-2\"),\n                                        children: \"Persentase Selesai\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-1 text-xs mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-emerald-700\",\n                                                        children: dashboardData.tasks_completed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-blue-700\",\n                                                        children: dashboardData.tasks_in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Proses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-red-700\",\n                                                        children: dashboardData.tasks_not_started\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(palette.grayLight),\n                                                        children: \"Belum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full text-xs\",\n                                        onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                        children: \"Detail Tugas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Log Mingguan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold \".concat(palette.goldDark),\n                                                children: dashboardData.weekly_logs_count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight}\",\n                                                children: \"Total Log\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 mb-3\",\n                                        children: [\n                                            dashboardData.recent_weekly_logs.slice(0, 2).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Minggu #\",\n                                                                log.week_number\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: log.notes_count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, log.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            dashboardData.recent_weekly_logs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\".concat(palette.grayLight, \" text-xs\"),\n                                                children: \"Tidak ada log\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full text-xs\",\n                                        onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                        children: \"Detail Log\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getStatusColor(dashboardData.status_project),\n                                            size: \"sm\",\n                                            children: dashboardData.status_project\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight} mb-1\",\n                                                children: \"Anggaran\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold \".concat(palette.goldDark),\n                                                children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight} mb-1\",\n                                                children: \"Kategori\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: dashboardData.project_category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                    children: \"Hapus Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogDescription, {\n                                    children: \"Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak dapat dibatalkan.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    disabled: deleteLoading,\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDelete,\n                                    disabled: deleteLoading,\n                                    children: deleteLoading ? 'Menghapus...' : 'Hapus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDashboardContent, \"8ZU5MIOROIm4/loS7tGVBmfEBL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC,\n        _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = ProjectDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx\n"));

/***/ })

});