"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,Target,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst menuItems = [\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Manajemen Akun',\n        href: '/account-management',\n        adminOnly: true\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: 'Presensi',\n        submenu: [\n            {\n                title: 'Daftar Presensi',\n                href: '/attendance'\n            },\n            {\n                title: 'Isi Presensi',\n                href: '/attendance/record'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: 'Manajemen Karyawan',\n        submenu: [\n            {\n                title: 'Daftar Karyawan',\n                href: '/employee'\n            },\n            {\n                title: 'Presensi Karyawan',\n                href: '/employee/attendance'\n            },\n            {\n                title: 'KPI Karyawan',\n                href: '/employee/kpi'\n            },\n            {\n                title: 'KPI Saya',\n                href: '/employee/mykpi'\n            },\n            {\n                title: 'Penggajian',\n                href: '/employee/salary'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: 'Manajemen Proyek',\n        submenu: [\n            {\n                title: 'Daftar Proyek',\n                href: '/project'\n            },\n            {\n                title: 'Dashboard Proyek',\n                href: '/project/dashboard'\n            },\n            {\n                title: 'Tugas Proyek',\n                href: '/project/task'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: 'KPI Proyek',\n        href: '/kpi-project',\n        allowedRoles: [\n            'Admin',\n            'Manager',\n            'Operation'\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: 'Manajemen Faktur',\n        href: '/invoice'\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        title: 'Manajemen Klien',\n        href: '/client'\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [openSections, setOpenSections] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const { isAdmin, hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__.useRBAC)();\n    // Function to get the parent section of a path\n    const getParentSection = (path)=>{\n        for (const item of menuItems){\n            if (item.submenu) {\n                for (const subItem of item.submenu){\n                    if (path === subItem.href) {\n                        return {\n                            parentTitle: item.title,\n                            isSubItemActive: true,\n                            activeSubItem: subItem.href\n                        };\n                    }\n                }\n            } else if (item.href === path) {\n                return {\n                    parentTitle: item.title,\n                    isSubItemActive: false,\n                    activeSubItem: null\n                };\n            }\n        }\n        return null;\n    };\n    // Get current active parent and subitem\n    const activeInfo = getParentSection(pathname);\n    // Initialize open sections based on current path\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeInfo && activeInfo.isSubItemActive) {\n                setOpenSections({\n                    \"Sidebar.useEffect\": (prev)=>{\n                        if (!prev.includes(activeInfo.parentTitle)) {\n                            return [\n                                ...prev,\n                                activeInfo.parentTitle\n                            ];\n                        }\n                        return prev;\n                    }\n                }[\"Sidebar.useEffect\"]);\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        activeInfo\n    ]);\n    const toggleSection = (title, e)=>{\n        e.preventDefault();\n        setOpenSections((prev)=>{\n            if (prev.includes(title)) {\n                return prev.filter((item)=>item !== title);\n            } else {\n                return [\n                    ...prev,\n                    title\n                ];\n            }\n        });\n    };\n    // Filter menu items based on role\n    const filteredMenuItems = menuItems.filter((item)=>{\n        // Hide admin-only items from non-admin users\n        if (item.adminOnly && !isAdmin()) {\n            return false;\n        }\n        // Hide items with role restrictions from users who don't have the required roles\n        if (item.allowedRoles && !hasRole(item.allowedRoles)) {\n            return false;\n        }\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-64 bg-gradient-to-b from-[#AB8B3B] to-[#8B7355] text-white shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex-1 space-y-2 px-3 py-4\",\n            children: filteredMenuItems.map((item)=>{\n                const isParentActive = (activeInfo === null || activeInfo === void 0 ? void 0 : activeInfo.parentTitle) === item.title;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                        open: openSections.includes(item.title),\n                        className: \"transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-lg overflow-hidden', isParentActive && 'bg-white/10 backdrop-blur-sm'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: (e)=>toggleSection(item.title, e),\n                                        className: \"flex w-full cursor-pointer items-center justify-between px-3 py-2.5 text-white hover:bg-white/10 hover:text-white rounded-lg transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_Target_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('h-4 w-4 transition-transform duration-300', openSections.includes(item.title) ? 'rotate-180' : '')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                    className: \"space-y-1 px-2 py-1\",\n                                    children: item.submenu.filter((subItem)=>{\n                                        // Hide admin-only submenu items from non-admin users\n                                        if (subItem.adminOnly && !isAdmin()) {\n                                            return false;\n                                        }\n                                        // Hide submenu items with role restrictions from users who don't have the required roles\n                                        if (subItem.allowedRoles && !hasRole(subItem.allowedRoles)) {\n                                            return false;\n                                        }\n                                        return true;\n                                    }).map((subItem)=>{\n                                        const isSubItemActive = pathname === subItem.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: subItem.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('block px-9 py-2 text-sm transition-colors rounded-md', isSubItemActive ? 'text-white bg-white/5 font-bold' : 'text-white/90 hover:text-white hover:bg-white/10'),\n                                            children: subItem.title\n                                        }, subItem.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 29\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 17\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-lg overflow-hidden', isParentActive && 'bg-white/10 backdrop-blur-sm'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center gap-2 px-3 py-2.5 rounded-lg transition-colors duration-200', isParentActive ? 'text-white font-bold' : 'text-white hover:text-white hover:bg-white/10'),\n                            children: [\n                                item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 17\n                    }, this)\n                }, item.title, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"O+REYlU6+q6IJn6FB6RXfEpfFSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__.useRBAC\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sidebar.tsx\n"));

/***/ })

});