"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations - White-Gold Theme\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#F3E8C7',\n                dark: '#F3E8C7'\n            },\n            fill: '#F3E8C7'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#374151',\n                dark: '#374151'\n            },\n            fill: '#374151'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category2: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category3: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category4: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category5: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: '#F3E8C7',\n                dark: '#F3E8C7'\n            },\n            fill: '#F3E8C7'\n        },\n        in_progress: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed_below_target: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        completed_on_target: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        completed_above_target: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        }\n    };\n    // Function to get status badge color - White-Gold Theme\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'completed':\n                return 'bg-yellow-200 text-yellow-900';\n            case 'cancelled':\n                return 'bg-gray-300 text-gray-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill - White-Gold Theme\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#F3E8C7' : 'bg-yellow-100';\n            case 'in_progress':\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n            case 'completed_below_target':\n                return forChart ? '#6B7280' : 'bg-gray-600';\n            case 'completed_on_target':\n                return forChart ? '#AB8B3B' : 'bg-yellow-600';\n            case 'completed_above_target':\n                return forChart ? '#8B7355' : 'bg-yellow-700';\n            default:\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n        }\n    };\n    // Function to get chart fill color for project status - White-Gold Theme\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#F3E8C7'; // Very Light Gold\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#D4B86A'; // Light Gold\n            case 'completed':\n            case 'selesai':\n                return '#AB8B3B'; // Primary Gold\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#374151'; // Dark Gray\n            default:\n                return '#F3E8C7'; // Very Light Gold\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories - Single Gold Color\n    const categoryColors = [\n        '#AB8B3B',\n        '#AB8B3B',\n        '#AB8B3B',\n        '#AB8B3B',\n        '#AB8B3B'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Selesai: \",\n                                            dashboardData.tasks.completed,\n                                            \" (\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Proyek dengan tenggat waktu dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 lg:grid-cols-4 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: statusChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                        data: statusData,\n                                                        dataKey: \"value\",\n                                                        nameKey: \"name\",\n                                                        cx: \"50%\",\n                                                        cy: \"50%\",\n                                                        outerRadius: 50,\n                                                        paddingAngle: 2,\n                                                        label: true,\n                                                        labelLine: false,\n                                                        children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                                fill: getStatusChartColor(entry.name)\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-1 text-xs mt-2\",\n                                        children: statusData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full\",\n                                                        style: {\n                                                            backgroundColor: getStatusChartColor(item.name)\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 truncate\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData.slice(0, 4),\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 10,\n                                                left: 5,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 2,\n                                                    children: [\n                                                        categoryData.slice(0, 4).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                                fill: categoryColors[index % categoryColors.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_23__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 4,\n                                                            className: \"fill-white\",\n                                                            fontSize: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_23__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 4,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData.slice(0, 4),\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 10,\n                                                left: 5,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 2,\n                                                    fill: \"#AB8B3B\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_23__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 4,\n                                                            className: \"fill-white\",\n                                                            fontSize: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_23__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 4,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: kpiChartConfig,\n                                            className: \"mx-auto aspect-square max-h-[120px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        cursor: false,\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                            hideLabel: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 30\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                        data: kpiStatusData,\n                                                        dataKey: \"value\",\n                                                        nameKey: \"name\",\n                                                        innerRadius: 25,\n                                                        outerRadius: 40,\n                                                        paddingAngle: 2,\n                                                        label: (param)=>{\n                                                            let { value, cx, cy, midAngle, innerRadius, outerRadius } = param;\n                                                            if (value === 0) return '';\n                                                            const RADIAN = Math.PI / 180;\n                                                            const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n                                                            const x = cx + radius * Math.cos(-midAngle * RADIAN);\n                                                            const y = cy + radius * Math.sin(-midAngle * RADIAN);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: x,\n                                                                y: y,\n                                                                fill: \"white\",\n                                                                textAnchor: x > cx ? 'start' : 'end',\n                                                                dominantBaseline: \"central\",\n                                                                fontSize: \"10\",\n                                                                fontWeight: \"bold\",\n                                                                children: value\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, void 0);\n                                                        },\n                                                        labelLine: false,\n                                                        children: [\n                                                            kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                                    fill: entry.fill\n                                                                }, \"cell-\".concat(entry.name), false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.Label, {\n                                                                content: (param)=>{\n                                                                    let { viewBox } = param;\n                                                                    if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            textAnchor: \"middle\",\n                                                                            dominantBaseline: \"middle\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                                x: viewBox.cx,\n                                                                                y: viewBox.cy,\n                                                                                className: \"fill-foreground text-lg font-bold\",\n                                                                                children: [\n                                                                                    totalKpiAchievement,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                                lineNumber: 566,\n                                                                                columnNumber: 31\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-1 text-xs mt-2\",\n                                        children: kpiStatusData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full\",\n                                                        style: {\n                                                            backgroundColor: item.fill\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 truncate\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-gray-500 text-gray-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});