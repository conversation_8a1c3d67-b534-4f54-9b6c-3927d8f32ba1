"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations - White-Gold Theme\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#374151',\n                dark: '#374151'\n            },\n            fill: '#374151'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category2: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        category3: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        },\n        category4: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        category5: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed_below_target: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        completed_on_target: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        completed_above_target: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        }\n    };\n    // Function to get status badge color - White-Gold Theme\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'completed':\n                return 'bg-yellow-200 text-yellow-900';\n            case 'cancelled':\n                return 'bg-gray-300 text-gray-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill - White-Gold Theme\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#9CA3AF' : 'bg-gray-500';\n            case 'in_progress':\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n            case 'completed_below_target':\n                return forChart ? '#6B7280' : 'bg-gray-600';\n            case 'completed_on_target':\n                return forChart ? '#AB8B3B' : 'bg-yellow-600';\n            case 'completed_above_target':\n                return forChart ? '#8B7355' : 'bg-yellow-700';\n            default:\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n        }\n    };\n    // Function to get chart fill color for project status - White-Gold Theme\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#9CA3AF'; // Light Gray\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#D4B86A'; // Light Gold\n            case 'completed':\n            case 'selesai':\n                return '#AB8B3B'; // Primary Gold\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#374151'; // Dark Gray\n            default:\n                return '#9CA3AF'; // Light Gray\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories - White-Gold Theme\n    const categoryColors = [\n        '#AB8B3B',\n        '#D4B86A',\n        '#8B7355',\n        '#6B7280',\n        '#9CA3AF'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Selesai: \",\n                                            dashboardData.tasks.completed,\n                                            \" (\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Proyek dengan tenggat waktu dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: statusChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                    data: statusData,\n                                                    dataKey: \"value\",\n                                                    nameKey: \"name\",\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 80,\n                                                    paddingAngle: 2,\n                                                    label: true,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: getStatusChartColor(entry.name)\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegend, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegendContent, {\n                                                        nameKey: \"name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 30,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    width: 120\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    children: categoryData.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: categoryColors[index % categoryColors.length]\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 50,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    cursor: false,\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    fill: \"#AB8B3B\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 8,\n                                                            className: \"fill-[--color-label]\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 8,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex-1 pb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                    config: kpiChartConfig,\n                                    className: \"mx-auto aspect-square max-h-[250px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                cursor: false,\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                    hideLabel: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 28\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                data: kpiStatusData,\n                                                dataKey: \"value\",\n                                                nameKey: \"name\",\n                                                innerRadius: 60,\n                                                outerRadius: 80,\n                                                paddingAngle: 2,\n                                                children: [\n                                                    kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(entry.name), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                        content: (param)=>{\n                                                            let { viewBox } = param;\n                                                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                    x: viewBox.cx,\n                                                                    y: viewBox.cy,\n                                                                    textAnchor: \"middle\",\n                                                                    dominantBaseline: \"middle\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            className: \"fill-foreground text-3xl font-bold\",\n                                                                            children: [\n                                                                                totalKpiAchievement,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: (viewBox.cy || 0) + 24,\n                                                                            className: \"fill-muted-foreground text-sm\",\n                                                                            children: \"Pencapaian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-col gap-2 text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 font-medium leading-none justify-center\",\n                                        children: dashboardData.kpis.achievement_percentage >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian melebihi target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : dashboardData.kpis.achievement_percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian mendekati target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian perlu ditingkatkan\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-gray-500 text-gray-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});