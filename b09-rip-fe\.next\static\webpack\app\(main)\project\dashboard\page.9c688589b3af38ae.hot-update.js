"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChartNoAxesColumn)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.479.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"line\",\n        {\n            x1: \"18\",\n            x2: \"18\",\n            y1: \"20\",\n            y2: \"10\",\n            key: \"1xfpm4\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"20\",\n            y2: \"4\",\n            key: \"be30l9\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6\",\n            y1: \"20\",\n            y2: \"14\",\n            key: \"1r4le6\"\n        }\n    ]\n];\nconst ChartNoAxesColumn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChartNoAxesColumn\", __iconNode);\n //# sourceMappingURL=chart-no-axes-column.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.479.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n];\nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", __iconNode);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ SquareCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.479.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5\",\n            key: \"1uzm8b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst SquareCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"SquareCheckBig\", __iconNode);\n //# sourceMappingURL=square-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Target)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.479.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"6\",\n            key: \"1vlfrh\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"2\",\n            key: \"1c9p78\"\n        }\n    ]\n];\nconst Target = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Target\", __iconNode);\n //# sourceMappingURL=target.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.479.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingUp\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartesianGrid: () => (/* binding */ CartesianGrid)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/isFunction */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isFunction.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_isFunction__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _util_LogUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/LogUtils */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/util/LogUtils.js\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/util/ReactUtils.js\");\n/* harmony import */ var _util_ChartUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/ChartUtils */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/util/ChartUtils.js\");\n/* harmony import */ var _getTicks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getTicks */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/getTicks.js\");\n/* harmony import */ var _CartesianAxis__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CartesianAxis */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianAxis.js\");\n/* harmony import */ var _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/chartLayoutContext */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/context/chartLayoutContext.js\");\nvar _s = $RefreshSig$();\nvar _excluded = [\n    \"x1\",\n    \"y1\",\n    \"x2\",\n    \"y2\",\n    \"key\"\n], _excluded2 = [\n    \"offset\"\n];\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n/**\n * @fileOverview Cartesian Grid\n */ \n\n\n\n\n\n\n\n\n/**\n * The <CartesianGrid horizontal\n */ var Background = function Background(props) {\n    var fill = props.fill;\n    if (!fill || fill === 'none') {\n        return null;\n    }\n    var fillOpacity = props.fillOpacity, x = props.x, y = props.y, width = props.width, height = props.height, ry = props.ry;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n        x: x,\n        y: y,\n        ry: ry,\n        width: width,\n        height: height,\n        stroke: \"none\",\n        fill: fill,\n        fillOpacity: fillOpacity,\n        className: \"recharts-cartesian-grid-bg\"\n    });\n};\n_c = Background;\nfunction renderLineItem(option, props) {\n    var lineItem;\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(option)) {\n        // @ts-expect-error typescript does not see the props type when cloning an element\n        lineItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(option, props);\n    } else if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default()(option)) {\n        lineItem = option(props);\n    } else {\n        var x1 = props.x1, y1 = props.y1, x2 = props.x2, y2 = props.y2, key = props.key, others = _objectWithoutProperties(props, _excluded);\n        var _filterProps = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_2__.filterProps)(others, false), __ = _filterProps.offset, restOfFilteredProps = _objectWithoutProperties(_filterProps, _excluded2);\n        lineItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"line\", _extends({}, restOfFilteredProps, {\n            x1: x1,\n            y1: y1,\n            x2: x2,\n            y2: y2,\n            fill: \"none\",\n            key: key\n        }));\n    }\n    return lineItem;\n}\nfunction HorizontalGridLines(props) {\n    var x = props.x, width = props.width, _props$horizontal = props.horizontal, horizontal = _props$horizontal === void 0 ? true : _props$horizontal, horizontalPoints = props.horizontalPoints;\n    if (!horizontal || !horizontalPoints || !horizontalPoints.length) {\n        return null;\n    }\n    var items = horizontalPoints.map(function(entry, i) {\n        var lineItemProps = _objectSpread(_objectSpread({}, props), {}, {\n            x1: x,\n            y1: entry,\n            x2: x + width,\n            y2: entry,\n            key: \"line-\".concat(i),\n            index: i\n        });\n        return renderLineItem(horizontal, lineItemProps);\n    });\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\", {\n        className: \"recharts-cartesian-grid-horizontal\"\n    }, items);\n}\n_c1 = HorizontalGridLines;\nfunction VerticalGridLines(props) {\n    var y = props.y, height = props.height, _props$vertical = props.vertical, vertical = _props$vertical === void 0 ? true : _props$vertical, verticalPoints = props.verticalPoints;\n    if (!vertical || !verticalPoints || !verticalPoints.length) {\n        return null;\n    }\n    var items = verticalPoints.map(function(entry, i) {\n        var lineItemProps = _objectSpread(_objectSpread({}, props), {}, {\n            x1: entry,\n            y1: y,\n            x2: entry,\n            y2: y + height,\n            key: \"line-\".concat(i),\n            index: i\n        });\n        return renderLineItem(vertical, lineItemProps);\n    });\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\", {\n        className: \"recharts-cartesian-grid-vertical\"\n    }, items);\n}\n_c2 = VerticalGridLines;\nfunction HorizontalStripes(props) {\n    var horizontalFill = props.horizontalFill, fillOpacity = props.fillOpacity, x = props.x, y = props.y, width = props.width, height = props.height, horizontalPoints = props.horizontalPoints, _props$horizontal2 = props.horizontal, horizontal = _props$horizontal2 === void 0 ? true : _props$horizontal2;\n    if (!horizontal || !horizontalFill || !horizontalFill.length) {\n        return null;\n    }\n    // Why =y -y? I was trying to find any difference that this makes, with floating point numbers and edge cases but ... nothing.\n    var roundedSortedHorizontalPoints = horizontalPoints.map(function(e) {\n        return Math.round(e + y - y);\n    }).sort(function(a, b) {\n        return a - b;\n    });\n    // Why is this condition `!==` instead of `<=` ?\n    if (y !== roundedSortedHorizontalPoints[0]) {\n        roundedSortedHorizontalPoints.unshift(0);\n    }\n    var items = roundedSortedHorizontalPoints.map(function(entry, i) {\n        // Why do we strip only the last stripe if it is invisible, and not all invisible stripes?\n        var lastStripe = !roundedSortedHorizontalPoints[i + 1];\n        var lineHeight = lastStripe ? y + height - entry : roundedSortedHorizontalPoints[i + 1] - entry;\n        if (lineHeight <= 0) {\n            return null;\n        }\n        var colorIndex = i % horizontalFill.length;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n            key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n            ,\n            y: entry,\n            x: x,\n            height: lineHeight,\n            width: width,\n            stroke: \"none\",\n            fill: horizontalFill[colorIndex],\n            fillOpacity: fillOpacity,\n            className: \"recharts-cartesian-grid-bg\"\n        });\n    });\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\", {\n        className: \"recharts-cartesian-gridstripes-horizontal\"\n    }, items);\n}\n_c3 = HorizontalStripes;\nfunction VerticalStripes(props) {\n    var _props$vertical2 = props.vertical, vertical = _props$vertical2 === void 0 ? true : _props$vertical2, verticalFill = props.verticalFill, fillOpacity = props.fillOpacity, x = props.x, y = props.y, width = props.width, height = props.height, verticalPoints = props.verticalPoints;\n    if (!vertical || !verticalFill || !verticalFill.length) {\n        return null;\n    }\n    var roundedSortedVerticalPoints = verticalPoints.map(function(e) {\n        return Math.round(e + x - x);\n    }).sort(function(a, b) {\n        return a - b;\n    });\n    if (x !== roundedSortedVerticalPoints[0]) {\n        roundedSortedVerticalPoints.unshift(0);\n    }\n    var items = roundedSortedVerticalPoints.map(function(entry, i) {\n        var lastStripe = !roundedSortedVerticalPoints[i + 1];\n        var lineWidth = lastStripe ? x + width - entry : roundedSortedVerticalPoints[i + 1] - entry;\n        if (lineWidth <= 0) {\n            return null;\n        }\n        var colorIndex = i % verticalFill.length;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n            key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n            ,\n            x: entry,\n            y: y,\n            width: lineWidth,\n            height: height,\n            stroke: \"none\",\n            fill: verticalFill[colorIndex],\n            fillOpacity: fillOpacity,\n            className: \"recharts-cartesian-grid-bg\"\n        });\n    });\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\", {\n        className: \"recharts-cartesian-gridstripes-vertical\"\n    }, items);\n}\n_c4 = VerticalStripes;\nvar defaultVerticalCoordinatesGenerator = function defaultVerticalCoordinatesGenerator(_ref, syncWithTicks) {\n    var xAxis = _ref.xAxis, width = _ref.width, height = _ref.height, offset = _ref.offset;\n    return (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_3__.getCoordinatesOfGrid)((0,_getTicks__WEBPACK_IMPORTED_MODULE_4__.getTicks)(_objectSpread(_objectSpread(_objectSpread({}, _CartesianAxis__WEBPACK_IMPORTED_MODULE_5__.CartesianAxis.defaultProps), xAxis), {}, {\n        ticks: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_3__.getTicksOfAxis)(xAxis, true),\n        viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n        }\n    })), offset.left, offset.left + offset.width, syncWithTicks);\n};\nvar defaultHorizontalCoordinatesGenerator = function defaultHorizontalCoordinatesGenerator(_ref2, syncWithTicks) {\n    var yAxis = _ref2.yAxis, width = _ref2.width, height = _ref2.height, offset = _ref2.offset;\n    return (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_3__.getCoordinatesOfGrid)((0,_getTicks__WEBPACK_IMPORTED_MODULE_4__.getTicks)(_objectSpread(_objectSpread(_objectSpread({}, _CartesianAxis__WEBPACK_IMPORTED_MODULE_5__.CartesianAxis.defaultProps), yAxis), {}, {\n        ticks: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_3__.getTicksOfAxis)(yAxis, true),\n        viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n        }\n    })), offset.top, offset.top + offset.height, syncWithTicks);\n};\nvar defaultProps = {\n    horizontal: true,\n    vertical: true,\n    // The ordinates of horizontal grid lines\n    horizontalPoints: [],\n    // The abscissas of vertical grid lines\n    verticalPoints: [],\n    stroke: '#ccc',\n    fill: 'none',\n    // The fill of colors of grid lines\n    verticalFill: [],\n    horizontalFill: []\n};\nfunction CartesianGrid(props) {\n    _s();\n    var _props$stroke, _props$fill, _props$horizontal3, _props$horizontalFill, _props$vertical3, _props$verticalFill;\n    var chartWidth = (0,_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useChartWidth)();\n    var chartHeight = (0,_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useChartHeight)();\n    var offset = (0,_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useOffset)();\n    var propsIncludingDefaults = _objectSpread(_objectSpread({}, props), {}, {\n        stroke: (_props$stroke = props.stroke) !== null && _props$stroke !== void 0 ? _props$stroke : defaultProps.stroke,\n        fill: (_props$fill = props.fill) !== null && _props$fill !== void 0 ? _props$fill : defaultProps.fill,\n        horizontal: (_props$horizontal3 = props.horizontal) !== null && _props$horizontal3 !== void 0 ? _props$horizontal3 : defaultProps.horizontal,\n        horizontalFill: (_props$horizontalFill = props.horizontalFill) !== null && _props$horizontalFill !== void 0 ? _props$horizontalFill : defaultProps.horizontalFill,\n        vertical: (_props$vertical3 = props.vertical) !== null && _props$vertical3 !== void 0 ? _props$vertical3 : defaultProps.vertical,\n        verticalFill: (_props$verticalFill = props.verticalFill) !== null && _props$verticalFill !== void 0 ? _props$verticalFill : defaultProps.verticalFill,\n        x: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(props.x) ? props.x : offset.left,\n        y: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(props.y) ? props.y : offset.top,\n        width: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(props.width) ? props.width : offset.width,\n        height: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(props.height) ? props.height : offset.height\n    });\n    var x = propsIncludingDefaults.x, y = propsIncludingDefaults.y, width = propsIncludingDefaults.width, height = propsIncludingDefaults.height, syncWithTicks = propsIncludingDefaults.syncWithTicks, horizontalValues = propsIncludingDefaults.horizontalValues, verticalValues = propsIncludingDefaults.verticalValues;\n    // @ts-expect-error the scale prop is mixed up - we need to untagle this at some point\n    var xAxis = (0,_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useArbitraryXAxis)();\n    // @ts-expect-error the scale prop is mixed up - we need to untagle this at some point\n    var yAxis = (0,_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useYAxisWithFiniteDomainOrRandom)();\n    if (!(0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(width) || width <= 0 || !(0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(height) || height <= 0 || !(0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(x) || x !== +x || !(0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_7__.isNumber)(y) || y !== +y) {\n        return null;\n    }\n    /*\n   * verticalCoordinatesGenerator and horizontalCoordinatesGenerator are defined\n   * outside of the propsIncludingDefaults because they were never part of the original props\n   * and they were never passed as a prop down to horizontal/vertical custom elements.\n   * If we add these two to propsIncludingDefaults then we are changing public API.\n   * Not a bad thing per se but also not necessary.\n   */ var verticalCoordinatesGenerator = propsIncludingDefaults.verticalCoordinatesGenerator || defaultVerticalCoordinatesGenerator;\n    var horizontalCoordinatesGenerator = propsIncludingDefaults.horizontalCoordinatesGenerator || defaultHorizontalCoordinatesGenerator;\n    var horizontalPoints = propsIncludingDefaults.horizontalPoints, verticalPoints = propsIncludingDefaults.verticalPoints;\n    // No horizontal points are specified\n    if ((!horizontalPoints || !horizontalPoints.length) && lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default()(horizontalCoordinatesGenerator)) {\n        var isHorizontalValues = horizontalValues && horizontalValues.length;\n        var generatorResult = horizontalCoordinatesGenerator({\n            yAxis: yAxis ? _objectSpread(_objectSpread({}, yAxis), {}, {\n                ticks: isHorizontalValues ? horizontalValues : yAxis.ticks\n            }) : undefined,\n            width: chartWidth,\n            height: chartHeight,\n            offset: offset\n        }, isHorizontalValues ? true : syncWithTicks);\n        (0,_util_LogUtils__WEBPACK_IMPORTED_MODULE_8__.warn)(Array.isArray(generatorResult), \"horizontalCoordinatesGenerator should return Array but instead it returned [\".concat(_typeof(generatorResult), \"]\"));\n        if (Array.isArray(generatorResult)) {\n            horizontalPoints = generatorResult;\n        }\n    }\n    // No vertical points are specified\n    if ((!verticalPoints || !verticalPoints.length) && lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default()(verticalCoordinatesGenerator)) {\n        var isVerticalValues = verticalValues && verticalValues.length;\n        var _generatorResult = verticalCoordinatesGenerator({\n            xAxis: xAxis ? _objectSpread(_objectSpread({}, xAxis), {}, {\n                ticks: isVerticalValues ? verticalValues : xAxis.ticks\n            }) : undefined,\n            width: chartWidth,\n            height: chartHeight,\n            offset: offset\n        }, isVerticalValues ? true : syncWithTicks);\n        (0,_util_LogUtils__WEBPACK_IMPORTED_MODULE_8__.warn)(Array.isArray(_generatorResult), \"verticalCoordinatesGenerator should return Array but instead it returned [\".concat(_typeof(_generatorResult), \"]\"));\n        if (Array.isArray(_generatorResult)) {\n            verticalPoints = _generatorResult;\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\", {\n        className: \"recharts-cartesian-grid\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Background, {\n        fill: propsIncludingDefaults.fill,\n        fillOpacity: propsIncludingDefaults.fillOpacity,\n        x: propsIncludingDefaults.x,\n        y: propsIncludingDefaults.y,\n        width: propsIncludingDefaults.width,\n        height: propsIncludingDefaults.height,\n        ry: propsIncludingDefaults.ry\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(HorizontalGridLines, _extends({}, propsIncludingDefaults, {\n        offset: offset,\n        horizontalPoints: horizontalPoints,\n        xAxis: xAxis,\n        yAxis: yAxis\n    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(VerticalGridLines, _extends({}, propsIncludingDefaults, {\n        offset: offset,\n        verticalPoints: verticalPoints,\n        xAxis: xAxis,\n        yAxis: yAxis\n    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(HorizontalStripes, _extends({}, propsIncludingDefaults, {\n        horizontalPoints: horizontalPoints\n    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(VerticalStripes, _extends({}, propsIncludingDefaults, {\n        verticalPoints: verticalPoints\n    })));\n}\n_s(CartesianGrid, \"oJSveW6ai53/7wJt4UHWH2usf5U=\", false, function() {\n    return [\n        _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useChartWidth,\n        _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useChartHeight,\n        _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useOffset,\n        _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useArbitraryXAxis,\n        _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_6__.useYAxisWithFiniteDomainOrRandom\n    ];\n});\n_c5 = CartesianGrid;\nCartesianGrid.displayName = 'CartesianGrid';\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Background\");\n$RefreshReg$(_c1, \"HorizontalGridLines\");\n$RefreshReg$(_c2, \"VerticalGridLines\");\n$RefreshReg$(_c3, \"HorizontalStripes\");\n$RefreshReg$(_c4, \"VerticalStripes\");\n$RefreshReg$(_c5, \"CartesianGrid\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#3B82F6',\n                dark: '#3B82F6'\n            },\n            fill: '#3B82F6'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#10B981',\n                dark: '#10B981'\n            },\n            fill: '#10B981'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#EF4444',\n                dark: '#EF4444'\n            },\n            fill: '#EF4444'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: 'hsl(var(--chart-1))',\n                dark: 'hsl(var(--chart-1))'\n            },\n            fill: 'hsl(var(--chart-1))'\n        },\n        category2: {\n            theme: {\n                light: 'hsl(var(--chart-2))',\n                dark: 'hsl(var(--chart-2))'\n            },\n            fill: 'hsl(var(--chart-2))'\n        },\n        category3: {\n            theme: {\n                light: 'hsl(var(--chart-3))',\n                dark: 'hsl(var(--chart-3))'\n            },\n            fill: 'hsl(var(--chart-3))'\n        },\n        category4: {\n            theme: {\n                light: 'hsl(var(--chart-4))',\n                dark: 'hsl(var(--chart-4))'\n            },\n            fill: 'hsl(var(--chart-4))'\n        },\n        category5: {\n            theme: {\n                light: 'hsl(var(--chart-5))',\n                dark: 'hsl(var(--chart-5))'\n            },\n            fill: 'hsl(var(--chart-5))'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: 'hsl(var(--chart-1))',\n                dark: 'hsl(var(--chart-1))'\n            },\n            fill: 'hsl(var(--chart-1))'\n        },\n        in_progress: {\n            theme: {\n                light: 'hsl(var(--chart-2))',\n                dark: 'hsl(var(--chart-2))'\n            },\n            fill: 'hsl(var(--chart-2))'\n        },\n        completed_below_target: {\n            theme: {\n                light: 'hsl(var(--chart-3))',\n                dark: 'hsl(var(--chart-3))'\n            },\n            fill: 'hsl(var(--chart-3))'\n        },\n        completed_on_target: {\n            theme: {\n                light: 'hsl(var(--chart-4))',\n                dark: 'hsl(var(--chart-4))'\n            },\n            fill: 'hsl(var(--chart-4))'\n        },\n        completed_above_target: {\n            theme: {\n                light: 'hsl(var(--chart-5))',\n                dark: 'hsl(var(--chart-5))'\n            },\n            fill: 'hsl(var(--chart-5))'\n        }\n    };\n    // Function to get status badge color\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#9CA3AF' : 'bg-gray-500';\n            case 'in_progress':\n                return forChart ? '#3B82F6' : 'bg-blue-600';\n            case 'completed_below_target':\n                return forChart ? '#FBBF24' : 'bg-yellow-500';\n            case 'completed_on_target':\n                return forChart ? '#10B981' : 'bg-green-500';\n            case 'completed_above_target':\n                return forChart ? '#059669' : 'bg-emerald-600';\n            default:\n                return forChart ? '#3B82F6' : 'bg-blue-600';\n        }\n    };\n    // Function to get chart fill color for project status\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#9CA3AF'; // Gray-400\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#3B82F6'; // Blue-500\n            case 'completed':\n            case 'selesai':\n                return '#10B981'; // Green-500\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#EF4444'; // Red-500\n            default:\n                return '#9CA3AF'; // Gray-400\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories\n    const categoryColors = [\n        'hsl(var(--chart-1))',\n        'hsl(var(--chart-2))',\n        'hsl(var(--chart-3))',\n        'hsl(var(--chart-4))',\n        'hsl(var(--chart-5))'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Selesai: \",\n                                            dashboardData.tasks.completed,\n                                            \" (\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Proyek dengan tenggat waktu dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: statusChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                    data: statusData,\n                                                    dataKey: \"value\",\n                                                    nameKey: \"name\",\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 80,\n                                                    paddingAngle: 2,\n                                                    label: true,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: getStatusChartColor(entry.name)\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegend, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegendContent, {\n                                                        nameKey: \"name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 30,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    width: 120\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    children: categoryData.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: \"hsl(var(--chart-\".concat(index % 5 + 1, \"))\")\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 50,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    cursor: false,\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    fill: \"#AB8B3B\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 8,\n                                                            className: \"fill-[--color-label]\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 8,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex-1 pb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                    config: kpiChartConfig,\n                                    className: \"mx-auto aspect-square max-h-[250px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                cursor: false,\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                    hideLabel: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 28\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                data: kpiStatusData,\n                                                dataKey: \"value\",\n                                                nameKey: \"name\",\n                                                innerRadius: 60,\n                                                outerRadius: 80,\n                                                paddingAngle: 2,\n                                                children: [\n                                                    kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(entry.name), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                        content: (param)=>{\n                                                            let { viewBox } = param;\n                                                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                    x: viewBox.cx,\n                                                                    y: viewBox.cy,\n                                                                    textAnchor: \"middle\",\n                                                                    dominantBaseline: \"middle\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            className: \"fill-foreground text-3xl font-bold\",\n                                                                            children: [\n                                                                                totalKpiAchievement,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: (viewBox.cy || 0) + 24,\n                                                                            className: \"fill-muted-foreground text-sm\",\n                                                                            children: \"Pencapaian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-col gap-2 text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 font-medium leading-none justify-center\",\n                                        children: dashboardData.kpis.achievement_percentage >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian melebihi target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : dashboardData.kpis.achievement_percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian mendekati target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian perlu ditingkatkan\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-green-500 text-green-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});