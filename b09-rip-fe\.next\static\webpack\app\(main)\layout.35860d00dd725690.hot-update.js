"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Clock,FileText,Laptop,UserCog,Users,Users2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst menuItems = [\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Manajemen Akun',\n        href: '/account-management',\n        adminOnly: true\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: 'Presensi',\n        submenu: [\n            {\n                title: 'Daftar Presensi',\n                href: '/attendance'\n            },\n            {\n                title: 'Isi Presensi',\n                href: '/attendance/record'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: 'Manajemen Karyawan',\n        submenu: [\n            {\n                title: 'Daftar Karyawan',\n                href: '/employee'\n            },\n            {\n                title: 'Presensi Karyawan',\n                href: '/employee/attendance'\n            },\n            {\n                title: 'KPI Karyawan',\n                href: '/employee/kpi'\n            },\n            {\n                title: 'KPI Saya',\n                href: '/employee/mykpi'\n            },\n            {\n                title: 'Penggajian',\n                href: '/employee/salary'\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: 'Manajemen Proyek',\n        submenu: [\n            {\n                title: 'Daftar Proyek',\n                href: '/project'\n            },\n            {\n                title: 'Dashboard Proyek',\n                href: '/project/dashboard'\n            },\n            {\n                title: 'Tugas Proyek',\n                href: '/project/task'\n            },\n            {\n                title: 'KPI Proyek',\n                href: '/kpi-project',\n                allowedRoles: [\n                    'Admin',\n                    'Manager',\n                    'Operation'\n                ]\n            }\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: 'Manajemen Faktur',\n        href: '/invoice'\n    },\n    {\n        icon: _barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: 'Manajemen Klien',\n        href: '/client'\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [openSections, setOpenSections] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const { isAdmin, hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__.useRBAC)();\n    // Function to get the parent section of a path\n    const getParentSection = (path)=>{\n        for (const item of menuItems){\n            if (item.submenu) {\n                for (const subItem of item.submenu){\n                    if (path === subItem.href) {\n                        return {\n                            parentTitle: item.title,\n                            isSubItemActive: true,\n                            activeSubItem: subItem.href\n                        };\n                    }\n                }\n            } else if (item.href === path) {\n                return {\n                    parentTitle: item.title,\n                    isSubItemActive: false,\n                    activeSubItem: null\n                };\n            }\n        }\n        return null;\n    };\n    // Get current active parent and subitem\n    const activeInfo = getParentSection(pathname);\n    // Initialize open sections based on current path\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeInfo && activeInfo.isSubItemActive) {\n                setOpenSections({\n                    \"Sidebar.useEffect\": (prev)=>{\n                        if (!prev.includes(activeInfo.parentTitle)) {\n                            return [\n                                ...prev,\n                                activeInfo.parentTitle\n                            ];\n                        }\n                        return prev;\n                    }\n                }[\"Sidebar.useEffect\"]);\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        activeInfo\n    ]);\n    const toggleSection = (title, e)=>{\n        e.preventDefault();\n        setOpenSections((prev)=>{\n            if (prev.includes(title)) {\n                return prev.filter((item)=>item !== title);\n            } else {\n                return [\n                    ...prev,\n                    title\n                ];\n            }\n        });\n    };\n    // Filter menu items based on role\n    const filteredMenuItems = menuItems.filter((item)=>{\n        // Hide admin-only items from non-admin users\n        if (item.adminOnly && !isAdmin()) {\n            return false;\n        }\n        // Hide items with role restrictions from users who don't have the required roles\n        if (item.allowedRoles && !hasRole(item.allowedRoles)) {\n            return false;\n        }\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-64 bg-gradient-to-b from-[#AB8B3B] to-[#8B7355] text-white shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex-1 space-y-2 px-3 py-4\",\n            children: filteredMenuItems.map((item)=>{\n                const isParentActive = (activeInfo === null || activeInfo === void 0 ? void 0 : activeInfo.parentTitle) === item.title;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                        open: openSections.includes(item.title),\n                        className: \"transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-lg overflow-hidden', isParentActive && 'bg-white/10 backdrop-blur-sm'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: (e)=>toggleSection(item.title, e),\n                                        className: \"flex w-full cursor-pointer items-center justify-between px-3 py-2.5 text-white hover:bg-white/10 hover:text-white rounded-lg transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Clock_FileText_Laptop_UserCog_Users_Users2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('h-4 w-4 transition-transform duration-300', openSections.includes(item.title) ? 'rotate-180' : '')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                    className: \"space-y-1 px-2 py-1\",\n                                    children: item.submenu.filter((subItem)=>{\n                                        // Hide admin-only submenu items from non-admin users\n                                        if (subItem.adminOnly && !isAdmin()) {\n                                            return false;\n                                        }\n                                        // Hide submenu items with role restrictions from users who don't have the required roles\n                                        if (subItem.allowedRoles && !hasRole(subItem.allowedRoles)) {\n                                            return false;\n                                        }\n                                        return true;\n                                    }).map((subItem)=>{\n                                        const isSubItemActive = pathname === subItem.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: subItem.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('block px-9 py-2 text-sm transition-colors rounded-md', isSubItemActive ? 'text-white bg-white/5 font-bold' : 'text-white/90 hover:text-white hover:bg-white/10'),\n                                            children: subItem.title\n                                        }, subItem.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 29\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 17\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('rounded-lg overflow-hidden', isParentActive && 'bg-white/10 backdrop-blur-sm'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center gap-2 px-3 py-2.5 rounded-lg transition-colors duration-200', isParentActive ? 'text-white font-bold' : 'text-white hover:text-white hover:bg-white/10'),\n                            children: [\n                                item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 17\n                    }, this)\n                }, item.title, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"O+REYlU6+q6IJn6FB6RXfEpfFSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_6__.useRBAC\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sidebar.tsx\n"));

/***/ })

});