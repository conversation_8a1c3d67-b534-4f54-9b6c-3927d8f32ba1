"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations - White-Gold Theme\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#374151',\n                dark: '#374151'\n            },\n            fill: '#374151'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category2: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        category3: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        },\n        category4: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        category5: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed_below_target: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        completed_on_target: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        completed_above_target: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        }\n    };\n    // Function to get status badge color\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill - White-Gold Theme\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#9CA3AF' : 'bg-gray-500';\n            case 'in_progress':\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n            case 'completed_below_target':\n                return forChart ? '#6B7280' : 'bg-gray-600';\n            case 'completed_on_target':\n                return forChart ? '#AB8B3B' : 'bg-yellow-600';\n            case 'completed_above_target':\n                return forChart ? '#8B7355' : 'bg-yellow-700';\n            default:\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n        }\n    };\n    // Function to get chart fill color for project status - White-Gold Theme\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#9CA3AF'; // Light Gray\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#D4B86A'; // Light Gold\n            case 'completed':\n            case 'selesai':\n                return '#AB8B3B'; // Primary Gold\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#374151'; // Dark Gray\n            default:\n                return '#9CA3AF'; // Light Gray\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories - White-Gold Theme\n    const categoryColors = [\n        '#AB8B3B',\n        '#D4B86A',\n        '#8B7355',\n        '#6B7280',\n        '#9CA3AF'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-4 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 lg:grid-cols-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Dashboard Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-2\",\n                                        children: dashboardData.projects.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-32\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: statusChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_12__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_13__.Pie, {\n                                                        data: statusData,\n                                                        dataKey: \"value\",\n                                                        nameKey: \"name\",\n                                                        cx: \"50%\",\n                                                        cy: \"50%\",\n                                                        outerRadius: 40,\n                                                        paddingAngle: 2,\n                                                        children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                fill: getStatusChartColor(entry.name)\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Statistik KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-1\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-[#8B7355] mb-2\",\n                                        children: [\n                                            \"(\",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: kpiChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_12__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_13__.Pie, {\n                                                        data: kpiStatusData,\n                                                        dataKey: \"value\",\n                                                        nameKey: \"name\",\n                                                        innerRadius: 25,\n                                                        outerRadius: 35,\n                                                        paddingAngle: 2,\n                                                        children: kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                fill: entry.fill\n                                                            }, \"cell-\".concat(entry.name), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Statistik Tugas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-1\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-[#8B7355] mb-2\",\n                                        children: [\n                                            \"(\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: picChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_15__.BarChart, {\n                                                data: picData.slice(0, 3),\n                                                layout: \"vertical\",\n                                                margin: {\n                                                    top: 5,\n                                                    right: 10,\n                                                    left: 5,\n                                                    bottom: 5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.XAxis, {\n                                                        type: \"number\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.YAxis, {\n                                                        dataKey: \"name\",\n                                                        type: \"category\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Bar, {\n                                                        dataKey: \"value\",\n                                                        radius: 2,\n                                                        fill: \"#AB8B3B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium text-[#AB8B3B]\",\n                                    children: \"Tenggat & Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-[#AB8B3B] mb-1\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-2\",\n                                        children: \"30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                            config: categoryChartConfig,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_15__.BarChart, {\n                                                data: categoryData.slice(0, 3),\n                                                layout: \"vertical\",\n                                                margin: {\n                                                    top: 5,\n                                                    right: 10,\n                                                    left: 5,\n                                                    bottom: 5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.XAxis, {\n                                                        type: \"number\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.YAxis, {\n                                                        dataKey: \"name\",\n                                                        type: \"category\",\n                                                        hide: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Bar, {\n                                                        dataKey: \"value\",\n                                                        radius: 2,\n                                                        children: categoryData.slice(0, 3).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                fill: categoryColors[index]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 42\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-gradient-to-br from-[#AB8B3B]/5 to-[#D4B86A]/5 border-[#AB8B3B]/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg text-[#AB8B3B]\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 border border-[#AB8B3B]/10 rounded-lg p-3 hover:shadow-sm transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1 text-[#AB8B3B] hover:text-[#8B7355]\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-gradient-to-br from-[#AB8B3B]/5 to-[#D4B86A]/5 border-[#AB8B3B]/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg text-[#AB8B3B]\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 border border-[#AB8B3B]/10 rounded-lg p-3 hover:shadow-sm transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-green-500 text-green-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1 text-[#AB8B3B] hover:text-[#8B7355]\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});