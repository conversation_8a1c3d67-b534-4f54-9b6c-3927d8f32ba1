'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  ChevronDown,
  Users,
  UserCog,
  Users2,
  FileText,
  Clock,
  Laptop,
  Target,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { useRBAC } from '@/hooks/useRBAC';
import { UserRole } from '@/types/auth';

interface MenuItem {
  icon?: React.ElementType;
  title: string;
  href?: string;
  adminOnly?: boolean;
  allowedRoles?: UserRole[];
  submenu?: {
    title: string;
    href: string;
    isSpecial?: boolean;
    adminOnly?: boolean;
    allowedRoles?: UserRole[];
  }[];
}

const menuItems: MenuItem[] = [
  {
    icon: UserCog,
    title: 'Manaj<PERSON>en Akun',
    href: '/account-management',
    adminOnly: true,
  },
  {
    icon: Clock,
    title: 'Presensi',
    submenu: [
      { title: 'Daftar Presensi', href: '/attendance' },
      { title: '<PERSON><PERSON>sensi', href: '/attendance/record' },
    ],
  },
  {
    icon: Users,
    title: 'Manajemen Karyawan',
    submenu: [
      { title: 'Daftar Karyawan', href: '/employee' },
      { title: 'Presensi Karyawan', href: '/employee/attendance' },
      { title: 'K<PERSON> Karyawan', href: '/employee/kpi' },
      { title: 'KPI Saya', href: '/employee/mykpi' },
      { title: 'Penggajian', href: '/employee/salary' },
    ],
  },
  {
    icon: Laptop,
    title: 'Manajemen Proyek',
    submenu: [
      { title: 'Daftar Proyek', href: '/project' },
      { title: 'Dashboard Proyek', href: '/project/dashboard' },
      { title: 'Tugas Proyek', href: '/project/task' },
    ],
  },
  {
    icon: Target,
    title: 'KPI Proyek',
    href: '/kpi-project',
    allowedRoles: ['Admin', 'Manager', 'Operation'],
  },
  {
    icon: FileText,
    title: 'Manajemen Faktur',
    href: '/invoice',
  },
  {
    icon: Users2,
    title: 'Manajemen Klien',
    href: '/client',
  },
  // {
  //   icon: Headphones,
  //   title: 'Kasuat AI',
  //   href: '/ai',
  // },
];

export function Sidebar() {
  const pathname = usePathname();
  const [openSections, setOpenSections] = React.useState<string[]>([]);
  const { isAdmin, hasRole } = useRBAC();

  // Function to get the parent section of a path
  const getParentSection = (path: string | undefined) => {
    for (const item of menuItems) {
      if (item.submenu) {
        for (const subItem of item.submenu) {
          if (path === subItem.href) {
            return {
              parentTitle: item.title,
              isSubItemActive: true,
              activeSubItem: subItem.href,
            };
          }
        }
      } else if (item.href === path) {
        return {
          parentTitle: item.title,
          isSubItemActive: false,
          activeSubItem: null,
        };
      }
    }
    return null;
  };

  // Get current active parent and subitem
  const activeInfo = getParentSection(pathname);

  // Initialize open sections based on current path
  React.useEffect(() => {
    if (activeInfo && activeInfo.isSubItemActive) {
      setOpenSections((prev) => {
        if (!prev.includes(activeInfo.parentTitle)) {
          return [...prev, activeInfo.parentTitle];
        }
        return prev;
      });
    }
  }, [pathname, activeInfo]);

  const toggleSection = (
    title: string,
    e: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    e.preventDefault();

    setOpenSections((prev) => {
      if (prev.includes(title)) {
        return prev.filter((item) => item !== title);
      } else {
        return [...prev, title];
      }
    });
  };

  // Filter menu items based on role
  const filteredMenuItems = menuItems.filter((item) => {
    // Hide admin-only items from non-admin users
    if (item.adminOnly && !isAdmin()) {
      return false;
    }
    // Hide items with role restrictions from users who don't have the required roles
    if (item.allowedRoles && !hasRole(item.allowedRoles)) {
      return false;
    }
    return true;
  });

  return (
    <div className="w-64 bg-gradient-to-b from-[#AB8B3B] to-[#8B7355] text-white shadow-lg">
      <nav className="flex-1 space-y-2 px-3 py-4">
        {filteredMenuItems.map((item) => {
          const isParentActive = activeInfo?.parentTitle === item.title;

          return (
            <div key={item.title}>
              {item.submenu ? (
                <Collapsible
                  open={openSections.includes(item.title)}
                  className="transition-all duration-300 ease-in-out"
                >
                  <div
                    className={cn(
                      'rounded-lg overflow-hidden',
                      isParentActive && 'bg-white/20 backdrop-blur-sm'
                    )}
                  >
                    <CollapsibleTrigger asChild>
                      <div
                        onClick={(e) => toggleSection(item.title, e)}
                        className="flex w-full cursor-pointer items-center justify-between px-3 py-2.5 text-white hover:bg-white/10 hover:text-white rounded-lg transition-colors duration-200"
                      >
                        <div className="flex items-center gap-2">
                          {item.icon && <item.icon className="h-5 w-5" />}
                          <span>{item.title}</span>
                        </div>
                        <ChevronDown
                          className={cn(
                            'h-4 w-4 transition-transform duration-300',
                            openSections.includes(item.title)
                              ? 'rotate-180'
                              : ''
                          )}
                        />
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="space-y-1 px-2 py-1">
                      {item.submenu
                        .filter((subItem) => {
                          // Hide admin-only submenu items from non-admin users
                          if (subItem.adminOnly && !isAdmin()) {
                            return false;
                          }
                          // Hide submenu items with role restrictions from users who don't have the required roles
                          if (
                            subItem.allowedRoles &&
                            !hasRole(subItem.allowedRoles)
                          ) {
                            return false;
                          }
                          return true;
                        })
                        .map((subItem) => {
                          const isSubItemActive = pathname === subItem.href;
                          return (
                            <Link
                              key={subItem.href}
                              href={subItem.href}
                              className={cn(
                                'block px-9 py-2 text-sm transition-colors rounded-md',
                                isSubItemActive
                                  ? 'text-gray-800 font-medium'
                                  : 'text-white/90 hover:text-white hover:bg-white/10'
                              )}
                            >
                              {subItem.title}
                            </Link>
                          );
                        })}
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              ) : (
                <div
                  className={cn(
                    'rounded-lg overflow-hidden',
                    isParentActive && 'bg-white/20 backdrop-blur-sm'
                  )}
                >
                  <Link
                    href={item.href!}
                    className={cn(
                      'flex items-center gap-2 px-3 py-2.5 rounded-lg transition-colors duration-200',
                      isParentActive
                        ? 'text-[#FFE4B5] font-medium'
                        : 'text-white hover:text-white hover:bg-white/10'
                    )}
                  >
                    {item.icon && <item.icon className="h-5 w-5" />}
                    <span>{item.title}</span>
                  </Link>
                </div>
              )}
            </div>
          );
        })}
      </nav>
    </div>
  );
}
