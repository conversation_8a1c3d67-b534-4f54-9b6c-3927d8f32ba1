"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx":
/*!************************************************************!*\
  !*** ./src/components/project/ProjectDashboardContent.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDashboardContent: () => (/* binding */ ProjectDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/scroll.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/BackButton */ \"(app-pages-browser)/./src/components/ui/BackButton.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/format */ \"(app-pages-browser)/./src/lib/utils/format.ts\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useProjectDashboard */ \"(app-pages-browser)/./src/hooks/useProjectDashboard.ts\");\n/* harmony import */ var _lib_api_project__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api/project */ \"(app-pages-browser)/./src/lib/api/project.ts\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Enhanced Gold/White palette\nconst palette = {\n    bgMain: 'bg-[#fffbea]',\n    bgCard: 'bg-white',\n    bgCardSecondary: 'bg-[#fefcf3]',\n    gold: 'text-[#dfb14a]',\n    goldDark: 'text-[#b8941f]',\n    goldLight: 'text-[#f4d76a]',\n    goldBg: 'bg-[#fff2ce]',\n    goldAccent: 'bg-[#f7e3a1]',\n    goldGradient: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    goldGradientDark: 'bg-gradient-to-r from-[#dfb14a] to-[#b8941f]',\n    border: 'border-[#ecd9a0]',\n    borderLight: 'border-[#f5e8b8]',\n    progress: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    progressTrack: 'bg-[#fff8e1]',\n    green: 'text-emerald-700 bg-emerald-100',\n    blue: 'text-blue-700 bg-blue-100',\n    red: 'text-red-700 bg-red-100',\n    gray: 'text-gray-600 bg-gray-50',\n    grayDark: 'text-gray-700',\n    grayLight: 'text-gray-500',\n    shadow: 'shadow-sm shadow-[#f5e8b8]/20'\n};\nfunction ProjectDashboardContent(param) {\n    let { id } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC)();\n    const canEdit = hasRole([\n        'Operation',\n        'Manager'\n    ]);\n    const canDelete = hasRole([\n        'Manager'\n    ]);\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(id);\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteLoading, setDeleteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch full project data to get project_charter_id\n    const fetchProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProjectDashboardContent.useCallback[fetchProject]\": async ()=>{\n            if (!id) return;\n            try {\n                const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.getProjectById(id);\n                if (response.success && response.data) {\n                    setProject(response.data);\n                } else {\n                    console.error(\"Failed to fetch project: \".concat(response.message));\n                }\n            } catch (error) {\n                console.error('Error fetching project:', error);\n            }\n        }\n    }[\"ProjectDashboardContent.useCallback[fetchProject]\"], [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDashboardContent.useEffect\": ()=>{\n            fetchProject();\n        }\n    }[\"ProjectDashboardContent.useEffect\"], [\n        fetchProject\n    ]);\n    // Handle project deletion\n    const handleDelete = async ()=>{\n        if (!canDelete) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Anda tidak memiliki izin untuk menghapus proyek');\n            setDeleteDialogOpen(false);\n            return;\n        }\n        try {\n            setDeleteLoading(true);\n            const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.deleteProject(id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Proyek berhasil dihapus');\n                router.push('/project');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Gagal menghapus proyek: \".concat(response.message));\n            }\n        } catch (error) {\n            console.error('Error deleting project:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Terjadi kesalahan saat menghapus proyek');\n        } finally{\n            setDeleteLoading(false);\n            setDeleteDialogOpen(false);\n        }\n    };\n    // --- Status helpers\n    const statusColors = {\n        'Not Started': palette.gray,\n        'In Progress': palette.blue,\n        'Completed': palette.green,\n        'Cancelled': palette.red\n    };\n    const getStatusColor = (status)=>statusColors[status] || palette.gray;\n    const kpiColors = {\n        'not_started': palette.gray,\n        'in_progress': palette.blue,\n        'completed_below_target': 'bg-yellow-100 text-yellow-800',\n        'completed_on_target': palette.green,\n        'completed_above_target': 'bg-[#e7f8e5] text-[#107c41]'\n    };\n    const getKpiStatusColor = (status)=>kpiColors[status] || palette.gray;\n    const taskColors = {\n        'not_completed': palette.red,\n        'on_progress': palette.blue,\n        'completed': palette.green\n    };\n    const getTaskStatusColor = (status)=>taskColors[status] || palette.gray;\n    // Format status\n    const formatKpiStatus = (status)=>({\n            'not_started': 'Belum Dimulai',\n            'in_progress': 'Dalam Proses',\n            'completed_below_target': 'Selesai Di Bawah Target',\n            'completed_on_target': 'Selesai Sesuai Target',\n            'completed_above_target': 'Selesai Di Atas Target'\n        })[status] || status;\n    const formatTaskStatus = (status)=>({\n            'not_completed': 'Belum Selesai',\n            'on_progress': 'Dalam Proses',\n            'completed': 'Selesai'\n        })[status] || status;\n    // Calculate task completion percent\n    let taskPercent = 0;\n    let kpiPercent = 0;\n    if (dashboardData && dashboardData.tasks_total > 0) {\n        taskPercent = Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100);\n    }\n    if (dashboardData && dashboardData.kpi_count > 0 && dashboardData.kpis) {\n        const completedKpis = dashboardData.kpis.filter((kpi)=>kpi.status === 'completed_on_target' || kpi.status === 'completed_above_target').length;\n        kpiPercent = Math.round(completedKpis / dashboardData.kpi_count * 100);\n    }\n    // --- Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push(\"/project\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-2 lg:grid-cols-4 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push('/project')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border \".concat(palette.border, \" \").concat(palette.goldBg, \" \").concat(palette.shadow),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-3\",\n                                children: \"Coba Lagi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                                onClick: ()=>router.push(\"/project\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                                title: \"Dashboard Proyek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 flex-wrap\",\n                        children: [\n                            project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canEdit && (!project.project_charter_id || project.project_charter_id === 'TODO-project-charter-implementation') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter/create\")),\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.goldBg, \" border \").concat(palette.border, \" \").concat(palette.gold, \" hover:\").concat(palette.goldAccent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this),\n                                    project.project_charter_id && project.project_charter_id !== 'TODO-project-charter-implementation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter\")),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.gold, \" border-[#dfb14a]\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/detail\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Detail\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/gantt\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Gantt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Log\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"KPI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Tugas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                size: \"sm\",\n                                onClick: ()=>setDeleteDialogOpen(true),\n                                children: \"Hapus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-3 gap-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: dashboardData.project_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"\".concat(palette.gold, \" text-base\"),\n                                            children: dashboardData.organization_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(dashboardData.status_project),\n                                                    children: dashboardData.status_project\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"outline\",\n                                                    children: dashboardData.project_category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Tujuan:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: dashboardData.objectives\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Anggaran:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"col-span-2 \".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 mb-1\",\n                                                children: \"Progres Proyek\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center h-24 w-24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"absolute\",\n                                                        width: \"96\",\n                                                        height: \"96\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"#f7e3a1\",\n                                                                strokeWidth: \"12\",\n                                                                fill: \"none\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"#dfb14a\",\n                                                                strokeWidth: \"12\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: 2 * Math.PI * 40,\n                                                                strokeDashoffset: 2 * Math.PI * 40 * (1 - dashboardData.progress_percentage / 100),\n                                                                strokeLinecap: \"round\",\n                                                                style: {\n                                                                    transition: 'stroke-dashoffset 0.5s'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"\".concat(palette.gold, \" absolute font-bold text-2xl\"),\n                                                        children: [\n                                                            dashboardData.progress_percentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 text-xs mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"Hari Berlalu\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: dashboardData.days_elapsed\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"Tersisa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: dashboardData.days_remaining\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: dashboardData.days_total\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 mb-1\",\n                                                children: \"Timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-6 mb-2 w-full justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Mulai\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.start_project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Selesai\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.end_project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-3 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute w-full h-3 rounded-full\",\n                                                        style: {\n                                                            background: '#fff2ce'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute h-3 rounded-full\",\n                                                        style: {\n                                                            background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',\n                                                            width: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 w-4 h-4 rounded-full border-2 border-white\",\n                                                        style: {\n                                                            background: '#dfb14a',\n                                                            left: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\"),\n                                                            transform: 'translate(-50%,-25%)'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-3 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.kpi_count,\n                                                \" KPI\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center h-16 w-16\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"absolute\",\n                                                        width: \"64\",\n                                                        height: \"64\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"32\",\n                                                                cy: \"32\",\n                                                                r: \"26\",\n                                                                stroke: \"#f7e3a1\",\n                                                                strokeWidth: \"8\",\n                                                                fill: \"none\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"32\",\n                                                                cy: \"32\",\n                                                                r: \"26\",\n                                                                stroke: \"#dfb14a\",\n                                                                strokeWidth: \"8\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: 2 * Math.PI * 26,\n                                                                strokeDashoffset: 2 * Math.PI * 26 * (1 - kpiPercent / 100),\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute text-lg font-bold text-[#dfb14a]\",\n                                                        children: [\n                                                            kpiPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: [\n                                                    \"KPI Tercapai (\",\n                                                    kpiPercent,\n                                                    \"%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3 flex flex-col gap-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getKpiStatusColor(dashboardData.kpi_status),\n                                            children: formatKpiStatus(dashboardData.kpi_status)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.kpis.slice(0, 2).map((kpi)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-xs\",\n                                                                children: kpi.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            className: getKpiStatusColor(kpi.status),\n                                                            children: formatKpiStatus(kpi.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, kpi.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.kpis.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                        children: \"Lihat Semua KPI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.tasks_total,\n                                                \" Tugas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-4 rounded-full bg-[#fff2ce] relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 rounded-full\",\n                                                        style: {\n                                                            width: \"\".concat(taskPercent, \"%\"),\n                                                            background: 'linear-gradient(90deg,#ffe18f,#dfb14a)'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute right-2 top-0.5 text-xs font-bold text-[#dfb14a]\",\n                                                        children: [\n                                                            taskPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: [\n                                                    \"Penyelesaian Tugas (\",\n                                                    taskPercent,\n                                                    \"%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mt-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-emerald-700\",\n                                                        children: dashboardData.tasks_completed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-blue-700\",\n                                                        children: dashboardData.tasks_in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Proses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-red-700\",\n                                                        children: dashboardData.tasks_not_started\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Belum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.recent_tasks.slice(0, 2).map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: task.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            className: getTaskStatusColor(task.completion_status),\n                                                            children: formatTaskStatus(task.completion_status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, task.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.recent_tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                        children: \"Lihat Semua Tugas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.weekly_logs_count,\n                                                \" Log Mingguan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.recent_weekly_logs.slice(0, 2).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-xs\",\n                                                            children: [\n                                                                \"Minggu #\",\n                                                                log.week_number\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: [\n                                                                log.notes_count,\n                                                                \" catatan\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, log.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.recent_weekly_logs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada log mingguan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                        children: \"Lihat Semua Log\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                    children: \"Hapus Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogDescription, {\n                                    children: \"Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak dapat dibatalkan.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    disabled: deleteLoading,\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDelete,\n                                    disabled: deleteLoading,\n                                    children: deleteLoading ? 'Menghapus...' : 'Hapus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 566,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 565,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDashboardContent, \"8ZU5MIOROIm4/loS7tGVBmfEBL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC,\n        _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = ProjectDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx\n"));

/***/ })

});