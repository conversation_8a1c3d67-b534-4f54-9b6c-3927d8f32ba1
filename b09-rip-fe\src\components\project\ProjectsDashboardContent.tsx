'use client';

import { useRouter } from 'next/navigation';
import {
  Bar<PERSON>hart2,
  Target,
  CheckSquare,
  AlertCircle,
  RefreshCw,
  Clock,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTitle } from '@/components/ui/PageTitle';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/lib/utils/date';
import { useProjectsDashboard } from '@/hooks/useProjectsDashboard';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import * as RechartsPrimitive from 'recharts';
import { LabelList, Label } from 'recharts';

export function ProjectsDashboardContent() {
  const router = useRouter();
  const { dashboardData, loading, error, refreshDashboard } =
    useProjectsDashboard();

  // Chart configurations - White-Gold Theme
  const statusChartConfig = {
    not_started: {
      label: 'Belum Dimulai',
      theme: { light: '#F3E8C7', dark: '#F3E8C7' }, // Very Light Gold
      fill: '#F3E8C7',
    },
    in_progress: {
      label: 'Dalam Proses',
      theme: { light: '#D4B86A', dark: '#D4B86A' }, // Light Gold
      fill: '#D4B86A',
    },
    completed: {
      label: 'Selesai',
      theme: { light: '#AB8B3B', dark: '#AB8B3B' }, // Primary Gold
      fill: '#AB8B3B',
    },
    cancelled: {
      label: 'Dibatalkan',
      theme: { light: '#374151', dark: '#374151' }, // Dark Gray
      fill: '#374151',
    },
  };

  const categoryChartConfig = {
    category1: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' },
      fill: '#AB8B3B',
    },
    category2: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' },
      fill: '#AB8B3B',
    },
    category3: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' },
      fill: '#AB8B3B',
    },
    category4: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' },
      fill: '#AB8B3B',
    },
    category5: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' },
      fill: '#AB8B3B',
    },
  };

  const picChartConfig = {
    pic: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' }, // Amber-500
      fill: '#AB8B3B',
    },
    label: {
      color: 'hsl(var(--background))',
    },
  };

  const kpiChartConfig = {
    not_started: {
      theme: { light: '#F3E8C7', dark: '#F3E8C7' },
      fill: '#F3E8C7',
    },
    in_progress: {
      theme: { light: '#D4B86A', dark: '#D4B86A' },
      fill: '#D4B86A',
    },
    completed_below_target: {
      theme: { light: '#6B7280', dark: '#6B7280' },
      fill: '#6B7280',
    },
    completed_on_target: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' },
      fill: '#AB8B3B',
    },
    completed_above_target: {
      theme: { light: '#8B7355', dark: '#8B7355' },
      fill: '#8B7355',
    },
  };

  // Function to get status badge color - White-Gold Theme
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'not started':
      case 'not_started':
        return 'bg-gray-200 text-gray-800';
      case 'in progress':
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-yellow-200 text-yellow-900';
      case 'cancelled':
        return 'bg-gray-300 text-gray-800';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  // Function to get KPI status color and fill - White-Gold Theme
  const getKpiStatusColor = (status: string, forChart = false) => {
    switch (status.toLowerCase()) {
      case 'not_started':
        return forChart ? '#F3E8C7' : 'bg-yellow-100';
      case 'in_progress':
        return forChart ? '#D4B86A' : 'bg-yellow-400';
      case 'completed_below_target':
        return forChart ? '#6B7280' : 'bg-gray-600';
      case 'completed_on_target':
        return forChart ? '#AB8B3B' : 'bg-yellow-600';
      case 'completed_above_target':
        return forChart ? '#8B7355' : 'bg-yellow-700';
      default:
        return forChart ? '#D4B86A' : 'bg-yellow-400';
    }
  };

  // Function to get chart fill color for project status - White-Gold Theme
  const getStatusChartColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'not started':
      case 'not_started':
      case 'belum dimulai':
        return '#F3E8C7'; // Very Light Gold
      case 'in progress':
      case 'in_progress':
      case 'dalam proses':
        return '#D4B86A'; // Light Gold
      case 'completed':
      case 'selesai':
        return '#AB8B3B'; // Primary Gold
      case 'cancelled':
      case 'dibatalkan':
        return '#374151'; // Dark Gray
      default:
        return '#F3E8C7'; // Very Light Gold
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <PageTitle title="Dashboard Proyek" />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-40" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-10 w-20 mb-4" />
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error || !dashboardData) {
    return (
      <div className="container mx-auto py-6 px-6">
        <PageTitle title="Dashboard Proyek" />
        <Card className="bg-red-50 border-red-200 mt-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>Terjadi kesalahan saat memuat data dashboard.</p>
            </div>
            <p className="mt-2 text-sm text-red-600">
              {error || 'Data tidak tersedia'}
            </p>
            <Button
              onClick={refreshDashboard}
              variant="outline"
              className="mt-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Prepare data for charts
  const statusData = Object.entries(dashboardData.projects.by_status).map(
    ([key, value]) => {
      const colorKey = key.toLowerCase().replace(/ /g, '_');
      return {
        name: key,
        value,
        fill: `var(--color-${colorKey})`,
      };
    }
  );

  // Create an array of colors for categories - Single Gold Color
  const categoryColors = [
    '#AB8B3B',
    '#AB8B3B',
    '#AB8B3B',
    '#AB8B3B',
    '#AB8B3B',
  ];

  const categoryData = Object.entries(dashboardData.projects.by_category).map(
    ([key, value], index) => ({
      name: key,
      value,
      fill: categoryColors[index % categoryColors.length],
    })
  );

  const picData = dashboardData.projects.by_pic.map((pic) => ({
    name: pic.name,
    value: pic.count,
    fill: '#AB8B3B', // Amber-400
  }));

  // Prepare data for KPI donut chart
  const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map(
    ([key, value]) => {
      return {
        name: key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
        value,
        fill: getKpiStatusColor(key, true),
      };
    }
  );

  // Calculate total KPI achievement for donut chart center
  const totalKpiAchievement = dashboardData.kpis.achievement_percentage;

  // Filter upcoming deadlines to only show those within the next month (30 days)
  const upcomingDeadlinesNextMonth =
    dashboardData.projects.upcoming_deadlines.filter(
      (project) => project.days_remaining <= 30
    );

  return (
    <div className="container mx-auto py-6 px-6">
      <PageTitle title="Dashboard Proyek" />
      <p className="mt-2 text-gray-600">
        Ringkasan statistik untuk semua proyek.
      </p>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart2 className="h-4 w-4" />
              Total Proyek
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.projects.total}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Total KPI
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.kpis.total}</div>
            <div className="text-xs text-gray-500 mt-1">
              Pencapaian: {dashboardData.kpis.achievement_percentage}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckSquare className="h-4 w-4" />
              Total Tugas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.tasks.total}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Selesai: {dashboardData.tasks.completed} (
              {Math.round(
                (dashboardData.tasks.completed / dashboardData.tasks.total) *
                  100
              )}
              %)
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Tenggat Waktu
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {upcomingDeadlinesNextMonth.length}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Proyek dengan tenggat waktu dalam 30 hari mendatang
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section - All in One Row */}
      <div className="grid gap-4 lg:grid-cols-4 md:grid-cols-2 mt-6">
        {/* Projects by Status Chart */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Proyek berdasarkan Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ChartContainer config={statusChartConfig}>
                <RechartsPrimitive.PieChart>
                  <RechartsPrimitive.Pie
                    data={statusData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={50}
                    paddingAngle={2}
                    label
                  >
                    {statusData.map((entry, index) => (
                      <RechartsPrimitive.Cell
                        key={`cell-${index}`}
                        fill={getStatusChartColor(entry.name)}
                      />
                    ))}
                  </RechartsPrimitive.Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                </RechartsPrimitive.PieChart>
              </ChartContainer>
            </div>
            {/* Status Legend */}
            <div className="grid grid-cols-1 gap-1 text-xs mt-2">
              {statusData.map((item) => (
                <div key={item.name} className="flex items-center gap-2">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: getStatusChartColor(item.name) }}
                  ></div>
                  <span className="text-gray-600 truncate">{item.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Projects by Category Chart */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Proyek berdasarkan Kategori</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ChartContainer config={categoryChartConfig}>
                <RechartsPrimitive.BarChart
                  data={categoryData.slice(0, 4)}
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 10,
                    left: 5,
                    bottom: 5,
                  }}
                >
                  <RechartsPrimitive.XAxis type="number" hide />
                  <RechartsPrimitive.YAxis
                    dataKey="name"
                    type="category"
                    hide
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <RechartsPrimitive.Bar dataKey="value" radius={2}>
                    {categoryData.slice(0, 4).map((_, index) => (
                      <RechartsPrimitive.Cell
                        key={`cell-${index}`}
                        fill={categoryColors[index % categoryColors.length]}
                      />
                    ))}
                    <LabelList
                      dataKey="name"
                      position="insideLeft"
                      offset={4}
                      className="fill-white"
                      fontSize={10}
                    />
                    <LabelList
                      dataKey="value"
                      position="right"
                      offset={4}
                      className="fill-foreground"
                      fontSize={10}
                    />
                  </RechartsPrimitive.Bar>
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        {/* Projects by PIC Chart */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Proyek berdasarkan PIC</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ChartContainer config={picChartConfig}>
                <RechartsPrimitive.BarChart
                  data={picData.slice(0, 4)}
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 10,
                    left: 5,
                    bottom: 5,
                  }}
                >
                  <RechartsPrimitive.XAxis type="number" hide />
                  <RechartsPrimitive.YAxis
                    dataKey="name"
                    type="category"
                    hide
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <RechartsPrimitive.Bar
                    dataKey="value"
                    radius={2}
                    fill="#AB8B3B"
                  >
                    <LabelList
                      dataKey="name"
                      position="insideLeft"
                      offset={4}
                      className="fill-white"
                      fontSize={10}
                    />
                    <LabelList
                      dataKey="value"
                      position="right"
                      offset={4}
                      className="fill-foreground"
                      fontSize={10}
                    />
                  </RechartsPrimitive.Bar>
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        {/* KPI Status Donut Chart */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Status KPI</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ChartContainer
                config={kpiChartConfig}
                className="mx-auto aspect-square max-h-[120px]"
              >
                <RechartsPrimitive.PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <RechartsPrimitive.Pie
                    data={kpiStatusData}
                    dataKey="value"
                    nameKey="name"
                    innerRadius={30}
                    outerRadius={45}
                    paddingAngle={2}
                    label={({ value }) => value > 0 ? value : ''}
                    labelLine={false}
                  >
                    {kpiStatusData.map((entry) => (
                      <RechartsPrimitive.Cell
                        key={`cell-${entry.name}`}
                        fill={entry.fill}
                      />
                    ))}
                    <Label
                      content={({ viewBox }) => {
                        if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                          return (
                            <text
                              x={viewBox.cx}
                              y={viewBox.cy}
                              textAnchor="middle"
                              dominantBaseline="middle"
                            >
                              <tspan
                                x={viewBox.cx}
                                y={viewBox.cy}
                                className="fill-foreground text-lg font-bold"
                              >
                                {totalKpiAchievement}%
                              </tspan>
                            </text>
                          );
                        }
                      }}
                    />
                  </RechartsPrimitive.Pie>
                </RechartsPrimitive.PieChart>
              </ChartContainer>
            </div>

            {/* KPI Status Legend */}
            <div className="grid grid-cols-1 gap-1 text-xs mt-2">
              {kpiStatusData.map((item) => (
                <div key={item.name} className="flex items-center gap-2">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: item.fill }}
                  ></div>
                  <span className="text-gray-600 truncate">{item.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Projects and Upcoming Deadlines */}
      <div className="grid gap-6 md:grid-cols-2 mt-6">
        {/* Recent Projects */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Proyek Terbaru</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.projects.recent.map((project) => (
                <div key={project.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{project.project_name}</p>
                      <p className="text-sm text-gray-600">
                        {project.organization_name}
                      </p>
                    </div>
                    <Badge className={getStatusColor(project.status_project)}>
                      {project.status_project}
                    </Badge>
                  </div>
                  <div className="flex justify-between mt-2 text-sm">
                    <span className="text-gray-500">
                      PIC: {project.pic_name}
                    </span>
                    <span className="text-gray-500">
                      {project.days_remaining > 0
                        ? `${project.days_remaining} hari tersisa`
                        : 'Tenggat waktu terlewati'}
                    </span>
                  </div>
                  <Button
                    variant="link"
                    className="px-0 h-auto text-sm mt-1"
                    onClick={() => router.push(`/project/${project.id}`)}
                  >
                    Lihat Detail
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Deadlines */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Tenggat Waktu Mendatang (30 Hari)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingDeadlinesNextMonth.map((project) => (
                <div key={project.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <p className="font-medium">{project.project_name}</p>
                    <Badge
                      variant="outline"
                      className={
                        project.days_remaining <= 7
                          ? 'border-red-500 text-red-700'
                          : project.days_remaining <= 30
                            ? 'border-yellow-500 text-yellow-700'
                            : 'border-gray-500 text-gray-700'
                      }
                    >
                      {project.days_remaining} hari
                    </Badge>
                  </div>
                  <div className="flex justify-between mt-2 text-sm">
                    <span className="text-gray-500">
                      Tenggat: {formatDate(project.end_project)}
                    </span>
                    <span className="text-gray-500">
                      Progres: {project.progress_percentage}%
                    </span>
                  </div>
                  <Button
                    variant="link"
                    className="px-0 h-auto text-sm mt-1"
                    onClick={() => router.push(`/project/${project.id}`)}
                  >
                    Lihat Detail
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
