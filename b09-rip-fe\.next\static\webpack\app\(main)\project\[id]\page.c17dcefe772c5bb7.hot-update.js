"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx":
/*!************************************************************!*\
  !*** ./src/components/project/ProjectDashboardContent.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDashboardContent: () => (/* binding */ ProjectDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/scroll.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/BackButton */ \"(app-pages-browser)/./src/components/ui/BackButton.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/format */ \"(app-pages-browser)/./src/lib/utils/format.ts\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useProjectDashboard */ \"(app-pages-browser)/./src/hooks/useProjectDashboard.ts\");\n/* harmony import */ var _lib_api_project__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api/project */ \"(app-pages-browser)/./src/lib/api/project.ts\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Enhanced Gold/White palette\nconst palette = {\n    bgMain: 'bg-[#fffbea]',\n    bgCard: 'bg-white',\n    bgCardSecondary: 'bg-[#fefcf3]',\n    gold: 'text-[#dfb14a]',\n    goldDark: 'text-[#b8941f]',\n    goldLight: 'text-[#f4d76a]',\n    goldBg: 'bg-[#fff2ce]',\n    goldAccent: 'bg-[#f7e3a1]',\n    goldGradient: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    goldGradientDark: 'bg-gradient-to-r from-[#dfb14a] to-[#b8941f]',\n    border: 'border-[#ecd9a0]',\n    borderLight: 'border-[#f5e8b8]',\n    progress: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    progressTrack: 'bg-[#fff8e1]',\n    green: 'text-emerald-700 bg-emerald-100',\n    blue: 'text-blue-700 bg-blue-100',\n    red: 'text-red-700 bg-red-100',\n    gray: 'text-gray-600 bg-gray-50',\n    grayDark: 'text-gray-700',\n    grayLight: 'text-gray-500',\n    shadow: 'shadow-sm shadow-[#f5e8b8]/20'\n};\nfunction ProjectDashboardContent(param) {\n    let { id } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC)();\n    const canEdit = hasRole([\n        'Operation',\n        'Manager'\n    ]);\n    const canDelete = hasRole([\n        'Manager'\n    ]);\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(id);\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteLoading, setDeleteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch full project data to get project_charter_id\n    const fetchProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProjectDashboardContent.useCallback[fetchProject]\": async ()=>{\n            if (!id) return;\n            try {\n                const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.getProjectById(id);\n                if (response.success && response.data) {\n                    setProject(response.data);\n                } else {\n                    console.error(\"Failed to fetch project: \".concat(response.message));\n                }\n            } catch (error) {\n                console.error('Error fetching project:', error);\n            }\n        }\n    }[\"ProjectDashboardContent.useCallback[fetchProject]\"], [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDashboardContent.useEffect\": ()=>{\n            fetchProject();\n        }\n    }[\"ProjectDashboardContent.useEffect\"], [\n        fetchProject\n    ]);\n    // Handle project deletion\n    const handleDelete = async ()=>{\n        if (!canDelete) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Anda tidak memiliki izin untuk menghapus proyek');\n            setDeleteDialogOpen(false);\n            return;\n        }\n        try {\n            setDeleteLoading(true);\n            const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.deleteProject(id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Proyek berhasil dihapus');\n                router.push('/project');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Gagal menghapus proyek: \".concat(response.message));\n            }\n        } catch (error) {\n            console.error('Error deleting project:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Terjadi kesalahan saat menghapus proyek');\n        } finally{\n            setDeleteLoading(false);\n            setDeleteDialogOpen(false);\n        }\n    };\n    // --- Status helpers\n    const statusColors = {\n        'Not Started': palette.gray,\n        'In Progress': palette.blue,\n        'Completed': palette.green,\n        'Cancelled': palette.red\n    };\n    const getStatusColor = (status)=>statusColors[status] || palette.gray;\n    const kpiColors = {\n        'not_started': palette.gray,\n        'in_progress': palette.blue,\n        'completed_below_target': 'bg-yellow-100 text-yellow-800',\n        'completed_on_target': palette.green,\n        'completed_above_target': 'bg-[#e7f8e5] text-[#107c41]'\n    };\n    const getKpiStatusColor = (status)=>kpiColors[status] || palette.gray;\n    const taskColors = {\n        'not_completed': palette.red,\n        'on_progress': palette.blue,\n        'completed': palette.green\n    };\n    const getTaskStatusColor = (status)=>taskColors[status] || palette.gray;\n    // Format status\n    const formatKpiStatus = (status)=>({\n            'not_started': 'Belum Dimulai',\n            'in_progress': 'Dalam Proses',\n            'completed_below_target': 'Selesai Di Bawah Target',\n            'completed_on_target': 'Selesai Sesuai Target',\n            'completed_above_target': 'Selesai Di Atas Target'\n        })[status] || status;\n    const formatTaskStatus = (status)=>({\n            'not_completed': 'Belum Selesai',\n            'on_progress': 'Dalam Proses',\n            'completed': 'Selesai'\n        })[status] || status;\n    // Calculate task completion percent\n    let taskPercent = 0;\n    let kpiPercent = 0;\n    if (dashboardData && dashboardData.tasks_total > 0) {\n        taskPercent = Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100);\n    }\n    if (dashboardData && dashboardData.kpi_count > 0 && dashboardData.kpis) {\n        const completedKpis = dashboardData.kpis.filter((kpi)=>kpi.status === 'completed_on_target' || kpi.status === 'completed_above_target').length;\n        kpiPercent = Math.round(completedKpis / dashboardData.kpi_count * 100);\n    }\n    // --- Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push(\"/project\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-2 lg:grid-cols-4 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push('/project')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border \".concat(palette.border, \" \").concat(palette.goldBg, \" \").concat(palette.shadow),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-3\",\n                                children: \"Coba Lagi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                                onClick: ()=>router.push(\"/project\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                                title: \"Dashboard Proyek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 flex-wrap\",\n                        children: [\n                            project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canEdit && (!project.project_charter_id || project.project_charter_id === 'TODO-project-charter-implementation') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter/create\")),\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.goldBg, \" border \").concat(palette.border, \" \").concat(palette.gold, \" hover:\").concat(palette.goldAccent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this),\n                                    project.project_charter_id && project.project_charter_id !== 'TODO-project-charter-implementation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter\")),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.gold, \" border-[#dfb14a]\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/detail\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Detail\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/gantt\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Gantt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Log\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"KPI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Tugas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                size: \"sm\",\n                                onClick: ()=>setDeleteDialogOpen(true),\n                                children: \"Hapus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-4 gap-2 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"lg:col-span-2 \".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-bold mb-1\",\n                                            children: dashboardData.project_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"\".concat(palette.gold, \" text-sm mb-2\"),\n                                            children: dashboardData.organization_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(dashboardData.status_project),\n                                                    size: \"sm\",\n                                                    children: dashboardData.status_project\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: dashboardData.project_category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs \".concat(palette.grayLight),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Tujuan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" \",\n                                                        dashboardData.objectives\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Anggaran:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold \".concat(palette.goldDark, \" ml-1\"),\n                                                            children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs ${palette.grayLight} mb-1\",\n                                        children: \"Progres Proyek\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center justify-center h-16 w-16 mx-auto mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute\",\n                                                width: \"64\",\n                                                height: \"64\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"32\",\n                                                        cy: \"32\",\n                                                        r: \"26\",\n                                                        stroke: \"#fff8e1\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"32\",\n                                                        cy: \"32\",\n                                                        r: \"26\",\n                                                        stroke: \"#dfb14a\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\",\n                                                        strokeDasharray: 2 * Math.PI * 26,\n                                                        strokeDashoffset: 2 * Math.PI * 26 * (1 - dashboardData.progress_percentage / 100),\n                                                        strokeLinecap: \"round\",\n                                                        style: {\n                                                            transition: 'stroke-dashoffset 0.5s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\".concat(palette.goldDark, \" absolute font-bold text-lg\"),\n                                                children: [\n                                                    dashboardData.progress_percentage,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_elapsed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Berlalu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_remaining\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Tersisa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs ${palette.grayLight} mb-2\",\n                                        children: \"Timeline Proyek\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(palette.goldDark),\n                                                        children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.start_project)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Mulai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(palette.goldDark),\n                                                        children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.end_project)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-2 relative rounded-full\",\n                                        style: {\n                                            background: '#fff8e1'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute h-2 rounded-full\",\n                                                style: {\n                                                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',\n                                                    width: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 w-3 h-3 rounded-full border border-white\",\n                                                style: {\n                                                    background: '#dfb14a',\n                                                    left: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\"),\n                                                    transform: 'translate(-50%,-25%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-4 md:grid-cols-2 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold \".concat(palette.goldDark),\n                                                children: dashboardData.kpi_count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight}\",\n                                                children: \"Total KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center justify-center h-14 w-14 mx-auto mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute\",\n                                                width: \"56\",\n                                                height: \"56\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"28\",\n                                                        cy: \"28\",\n                                                        r: \"22\",\n                                                        stroke: \"#fff8e1\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"28\",\n                                                        cy: \"28\",\n                                                        r: \"22\",\n                                                        stroke: \"#dfb14a\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\",\n                                                        strokeDasharray: 2 * Math.PI * 22,\n                                                        strokeDashoffset: 2 * Math.PI * 22 * (1 - kpiPercent / 100),\n                                                        strokeLinecap: \"round\",\n                                                        style: {\n                                                            transition: 'stroke-dashoffset 0.5s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute text-sm font-bold \".concat(palette.goldDark),\n                                                children: [\n                                                    kpiPercent,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs ${palette.grayLight} mb-2\",\n                                        children: \"Pencapaian\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        className: getKpiStatusColor(dashboardData.kpi_status),\n                                        size: \"sm\",\n                                        children: formatKpiStatus(dashboardData.kpi_status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full mt-2 text-xs\",\n                                        onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                        children: \"Detail KPI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold \".concat(palette.goldDark),\n                                                children: dashboardData.tasks_total\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight}\",\n                                                children: \"Total Tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-3 rounded-full mb-2 relative\",\n                                        style: {\n                                            background: '#fff8e1'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 rounded-full\",\n                                                style: {\n                                                    width: \"\".concat(taskPercent, \"%\"),\n                                                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute right-1 top-0 text-xs font-bold \".concat(palette.goldDark, \" leading-3\"),\n                                                children: [\n                                                    taskPercent,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs ${palette.grayLight} mb-2\",\n                                        children: \"Persentase Selesai\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-1 text-xs mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-emerald-700\",\n                                                        children: dashboardData.tasks_completed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-blue-700\",\n                                                        children: dashboardData.tasks_in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Proses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-red-700\",\n                                                        children: dashboardData.tasks_not_started\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Belum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full text-xs\",\n                                        onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                        children: \"Detail Tugas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Log Mingguan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold \".concat(palette.goldDark),\n                                                children: dashboardData.weekly_logs_count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight}\",\n                                                children: \"Total Log\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 mb-3\",\n                                        children: [\n                                            dashboardData.recent_weekly_logs.slice(0, 2).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Minggu #\",\n                                                                log.week_number\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: log.notes_count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, log.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            dashboardData.recent_weekly_logs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\".concat(palette.grayLight, \" text-xs\"),\n                                                children: \"Tidak ada log\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full text-xs\",\n                                        onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                        children: \"Detail Log\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-1 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4 text-[#dfb14a]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getStatusColor(dashboardData.status_project),\n                                            size: \"sm\",\n                                            children: dashboardData.status_project\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight} mb-1\",\n                                                children: \"Anggaran\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold \".concat(palette.goldDark),\n                                                children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs ${palette.grayLight} mb-1\",\n                                                children: \"Kategori\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: dashboardData.project_category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                    children: \"Hapus Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogDescription, {\n                                    children: \"Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak dapat dibatalkan.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    disabled: deleteLoading,\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDelete,\n                                    disabled: deleteLoading,\n                                    children: deleteLoading ? 'Menghapus...' : 'Hapus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDashboardContent, \"8ZU5MIOROIm4/loS7tGVBmfEBL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC,\n        _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = ProjectDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx\n"));

/***/ })

});