"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx":
/*!************************************************************!*\
  !*** ./src/components/project/ProjectDashboardContent.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDashboardContent: () => (/* binding */ ProjectDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/scroll.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/BackButton */ \"(app-pages-browser)/./src/components/ui/BackButton.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/format */ \"(app-pages-browser)/./src/lib/utils/format.ts\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useProjectDashboard */ \"(app-pages-browser)/./src/hooks/useProjectDashboard.ts\");\n/* harmony import */ var _lib_api_project__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api/project */ \"(app-pages-browser)/./src/lib/api/project.ts\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Enhanced Gold/White palette\nconst palette = {\n    bgMain: 'bg-[#fffbea]',\n    bgCard: 'bg-white',\n    bgCardSecondary: 'bg-[#fefcf3]',\n    gold: 'text-[#dfb14a]',\n    goldDark: 'text-[#b8941f]',\n    goldLight: 'text-[#f4d76a]',\n    goldBg: 'bg-[#fff2ce]',\n    goldAccent: 'bg-[#f7e3a1]',\n    goldGradient: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    goldGradientDark: 'bg-gradient-to-r from-[#dfb14a] to-[#b8941f]',\n    border: 'border-[#ecd9a0]',\n    borderLight: 'border-[#f5e8b8]',\n    progress: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    progressTrack: 'bg-[#fff8e1]',\n    green: 'text-emerald-700 bg-emerald-100',\n    blue: 'text-blue-700 bg-blue-100',\n    red: 'text-red-700 bg-red-100',\n    gray: 'text-gray-600 bg-gray-50',\n    grayDark: 'text-gray-700',\n    grayLight: 'text-gray-500',\n    shadow: 'shadow-sm shadow-[#f5e8b8]/20'\n};\nfunction ProjectDashboardContent(param) {\n    let { id } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC)();\n    const canEdit = hasRole([\n        'Operation',\n        'Manager'\n    ]);\n    const canDelete = hasRole([\n        'Manager'\n    ]);\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(id);\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteLoading, setDeleteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch full project data to get project_charter_id\n    const fetchProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProjectDashboardContent.useCallback[fetchProject]\": async ()=>{\n            if (!id) return;\n            try {\n                const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.getProjectById(id);\n                if (response.success && response.data) {\n                    setProject(response.data);\n                } else {\n                    console.error(\"Failed to fetch project: \".concat(response.message));\n                }\n            } catch (error) {\n                console.error('Error fetching project:', error);\n            }\n        }\n    }[\"ProjectDashboardContent.useCallback[fetchProject]\"], [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDashboardContent.useEffect\": ()=>{\n            fetchProject();\n        }\n    }[\"ProjectDashboardContent.useEffect\"], [\n        fetchProject\n    ]);\n    // Handle project deletion\n    const handleDelete = async ()=>{\n        if (!canDelete) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Anda tidak memiliki izin untuk menghapus proyek');\n            setDeleteDialogOpen(false);\n            return;\n        }\n        try {\n            setDeleteLoading(true);\n            const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.deleteProject(id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Proyek berhasil dihapus');\n                router.push('/project');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Gagal menghapus proyek: \".concat(response.message));\n            }\n        } catch (error) {\n            console.error('Error deleting project:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Terjadi kesalahan saat menghapus proyek');\n        } finally{\n            setDeleteLoading(false);\n            setDeleteDialogOpen(false);\n        }\n    };\n    // --- Status helpers\n    const statusColors = {\n        'Not Started': palette.gray,\n        'In Progress': palette.blue,\n        'Completed': palette.green,\n        'Cancelled': palette.red\n    };\n    const getStatusColor = (status)=>statusColors[status] || palette.gray;\n    const kpiColors = {\n        'not_started': palette.gray,\n        'in_progress': palette.blue,\n        'completed_below_target': 'bg-yellow-100 text-yellow-800',\n        'completed_on_target': palette.green,\n        'completed_above_target': 'bg-[#e7f8e5] text-[#107c41]'\n    };\n    const getKpiStatusColor = (status)=>kpiColors[status] || palette.gray;\n    const taskColors = {\n        'not_completed': palette.red,\n        'on_progress': palette.blue,\n        'completed': palette.green\n    };\n    const getTaskStatusColor = (status)=>taskColors[status] || palette.gray;\n    // Format status\n    const formatKpiStatus = (status)=>({\n            'not_started': 'Belum Dimulai',\n            'in_progress': 'Dalam Proses',\n            'completed_below_target': 'Selesai Di Bawah Target',\n            'completed_on_target': 'Selesai Sesuai Target',\n            'completed_above_target': 'Selesai Di Atas Target'\n        })[status] || status;\n    const formatTaskStatus = (status)=>({\n            'not_completed': 'Belum Selesai',\n            'on_progress': 'Dalam Proses',\n            'completed': 'Selesai'\n        })[status] || status;\n    // Calculate task completion percent\n    let taskPercent = 0;\n    let kpiPercent = 0;\n    if (dashboardData && dashboardData.tasks_total > 0) {\n        taskPercent = Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100);\n    }\n    if (dashboardData && dashboardData.kpi_count > 0 && dashboardData.kpis) {\n        const completedKpis = dashboardData.kpis.filter((kpi)=>kpi.status === 'completed_on_target' || kpi.status === 'completed_above_target').length;\n        kpiPercent = Math.round(completedKpis / dashboardData.kpi_count * 100);\n    }\n    // --- Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push(\"/project\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-2 lg:grid-cols-4 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-32 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push('/project')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border \".concat(palette.border, \" \").concat(palette.goldBg, \" \").concat(palette.shadow),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-3\",\n                                children: \"Coba Lagi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(palette.bgMain, \" min-h-screen py-3 px-2\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                                onClick: ()=>router.push(\"/project\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                                title: \"Dashboard Proyek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 flex-wrap\",\n                        children: [\n                            project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canEdit && (!project.project_charter_id || project.project_charter_id === 'TODO-project-charter-implementation') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter/create\")),\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.goldBg, \" border \").concat(palette.border, \" \").concat(palette.gold, \" hover:\").concat(palette.goldAccent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this),\n                                    project.project_charter_id && project.project_charter_id !== 'TODO-project-charter-implementation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter\")),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"\".concat(palette.gold, \" border-[#dfb14a]\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/detail\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Detail\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/gantt\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Gantt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Log\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"KPI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Tugas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                size: \"sm\",\n                                onClick: ()=>setDeleteDialogOpen(true),\n                                children: \"Hapus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-4 gap-2 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"lg:col-span-2 \".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-bold mb-1\",\n                                            children: dashboardData.project_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"\".concat(palette.gold, \" text-sm mb-2\"),\n                                            children: dashboardData.organization_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(dashboardData.status_project),\n                                                    size: \"sm\",\n                                                    children: dashboardData.status_project\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: dashboardData.project_category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs ${palette.grayLight}\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Tujuan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" \",\n                                                        dashboardData.objectives\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Anggaran:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold ${palette.goldDark} ml-1\",\n                                                            children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs ${palette.grayLight} mb-1\",\n                                        children: \"Progres Proyek\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center justify-center h-16 w-16 mx-auto mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute\",\n                                                width: \"64\",\n                                                height: \"64\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"32\",\n                                                        cy: \"32\",\n                                                        r: \"26\",\n                                                        stroke: \"#fff8e1\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: \"32\",\n                                                        cy: \"32\",\n                                                        r: \"26\",\n                                                        stroke: \"#dfb14a\",\n                                                        strokeWidth: \"6\",\n                                                        fill: \"none\",\n                                                        strokeDasharray: 2 * Math.PI * 26,\n                                                        strokeDashoffset: 2 * Math.PI * 26 * (1 - dashboardData.progress_percentage / 100),\n                                                        strokeLinecap: \"round\",\n                                                        style: {\n                                                            transition: 'stroke-dashoffset 0.5s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\".concat(palette.goldDark, \" absolute font-bold text-lg\"),\n                                                children: [\n                                                    dashboardData.progress_percentage,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_elapsed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Berlalu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_remaining\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Tersisa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold \".concat(palette.goldDark),\n                                                        children: dashboardData.days_total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.borderLight, \" \").concat(palette.shadow),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs ${palette.grayLight} mb-2\",\n                                        children: \"Timeline Proyek\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(palette.goldDark),\n                                                        children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.start_project)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Mulai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(palette.goldDark),\n                                                        children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.end_project)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: palette.grayLight,\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-2 relative rounded-full\",\n                                        style: {\n                                            background: '#fff8e1'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute h-2 rounded-full\",\n                                                style: {\n                                                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',\n                                                    width: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 w-3 h-3 rounded-full border border-white\",\n                                                style: {\n                                                    background: '#dfb14a',\n                                                    left: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\"),\n                                                    transform: 'translate(-50%,-25%)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-3 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.kpi_count,\n                                                \" KPI\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center h-16 w-16\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"absolute\",\n                                                        width: \"64\",\n                                                        height: \"64\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"32\",\n                                                                cy: \"32\",\n                                                                r: \"26\",\n                                                                stroke: \"#f7e3a1\",\n                                                                strokeWidth: \"8\",\n                                                                fill: \"none\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"32\",\n                                                                cy: \"32\",\n                                                                r: \"26\",\n                                                                stroke: \"#dfb14a\",\n                                                                strokeWidth: \"8\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: 2 * Math.PI * 26,\n                                                                strokeDashoffset: 2 * Math.PI * 26 * (1 - kpiPercent / 100),\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute text-lg font-bold text-[#dfb14a]\",\n                                                        children: [\n                                                            kpiPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: [\n                                                    \"KPI Tercapai (\",\n                                                    kpiPercent,\n                                                    \"%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3 flex flex-col gap-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getKpiStatusColor(dashboardData.kpi_status),\n                                            children: formatKpiStatus(dashboardData.kpi_status)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.kpis.slice(0, 2).map((kpi)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-xs\",\n                                                                children: kpi.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            className: getKpiStatusColor(kpi.status),\n                                                            children: formatKpiStatus(kpi.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, kpi.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.kpis.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                        children: \"Lihat Semua KPI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.tasks_total,\n                                                \" Tugas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-4 rounded-full bg-[#fff2ce] relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 rounded-full\",\n                                                        style: {\n                                                            width: \"\".concat(taskPercent, \"%\"),\n                                                            background: 'linear-gradient(90deg,#ffe18f,#dfb14a)'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute right-2 top-0.5 text-xs font-bold text-[#dfb14a]\",\n                                                        children: [\n                                                            taskPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: [\n                                                    \"Penyelesaian Tugas (\",\n                                                    taskPercent,\n                                                    \"%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mt-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-emerald-700\",\n                                                        children: dashboardData.tasks_completed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-blue-700\",\n                                                        children: dashboardData.tasks_in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Proses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-red-700\",\n                                                        children: dashboardData.tasks_not_started\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Belum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.recent_tasks.slice(0, 2).map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: task.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            className: getTaskStatusColor(task.completion_status),\n                                                            children: formatTaskStatus(task.completion_status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, task.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.recent_tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                        children: \"Lihat Semua Tugas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.weekly_logs_count,\n                                                \" Log Mingguan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.recent_weekly_logs.slice(0, 2).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-xs\",\n                                                            children: [\n                                                                \"Minggu #\",\n                                                                log.week_number\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: [\n                                                                log.notes_count,\n                                                                \" catatan\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, log.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.recent_weekly_logs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada log mingguan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                        children: \"Lihat Semua Log\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                    children: \"Hapus Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogDescription, {\n                                    children: \"Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak dapat dibatalkan.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    disabled: deleteLoading,\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDelete,\n                                    disabled: deleteLoading,\n                                    children: deleteLoading ? 'Menghapus...' : 'Hapus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDashboardContent, \"8ZU5MIOROIm4/loS7tGVBmfEBL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC,\n        _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = ProjectDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx\n"));

/***/ })

});