"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations - White-Gold Theme\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#F3E8C7',\n                dark: '#F3E8C7'\n            },\n            fill: '#F3E8C7'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#374151',\n                dark: '#374151'\n            },\n            fill: '#374151'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category2: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category3: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category4: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category5: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            theme: {\n                light: '#D4B86A',\n                dark: '#D4B86A'\n            },\n            fill: '#D4B86A'\n        },\n        completed_below_target: {\n            theme: {\n                light: '#6B7280',\n                dark: '#6B7280'\n            },\n            fill: '#6B7280'\n        },\n        completed_on_target: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        completed_above_target: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        }\n    };\n    // Function to get status badge color - White-Gold Theme\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'completed':\n                return 'bg-yellow-200 text-yellow-900';\n            case 'cancelled':\n                return 'bg-gray-300 text-gray-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill - White-Gold Theme\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#9CA3AF' : 'bg-gray-500';\n            case 'in_progress':\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n            case 'completed_below_target':\n                return forChart ? '#6B7280' : 'bg-gray-600';\n            case 'completed_on_target':\n                return forChart ? '#AB8B3B' : 'bg-yellow-600';\n            case 'completed_above_target':\n                return forChart ? '#8B7355' : 'bg-yellow-700';\n            default:\n                return forChart ? '#D4B86A' : 'bg-yellow-400';\n        }\n    };\n    // Function to get chart fill color for project status - White-Gold Theme\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#F3E8C7'; // Very Light Gold\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#D4B86A'; // Light Gold\n            case 'completed':\n            case 'selesai':\n                return '#AB8B3B'; // Primary Gold\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#374151'; // Dark Gray\n            default:\n                return '#F3E8C7'; // Very Light Gold\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories - Single Gold Color\n    const categoryColors = [\n        '#AB8B3B',\n        '#AB8B3B',\n        '#AB8B3B',\n        '#AB8B3B',\n        '#AB8B3B'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Selesai: \",\n                                            dashboardData.tasks.completed,\n                                            \" (\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Proyek dengan tenggat waktu dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: statusChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                    data: statusData,\n                                                    dataKey: \"value\",\n                                                    nameKey: \"name\",\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 80,\n                                                    paddingAngle: 2,\n                                                    label: true,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: getStatusChartColor(entry.name)\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegend, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegendContent, {\n                                                        nameKey: \"name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 30,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    width: 120\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    children: categoryData.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: categoryColors[index % categoryColors.length]\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 50,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    cursor: false,\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    fill: \"#AB8B3B\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 8,\n                                                            className: \"fill-[--color-label]\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 8,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex-1 pb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                    config: kpiChartConfig,\n                                    className: \"mx-auto aspect-square max-h-[250px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                cursor: false,\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                    hideLabel: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 28\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                data: kpiStatusData,\n                                                dataKey: \"value\",\n                                                nameKey: \"name\",\n                                                innerRadius: 60,\n                                                outerRadius: 80,\n                                                paddingAngle: 2,\n                                                children: [\n                                                    kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(entry.name), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                        content: (param)=>{\n                                                            let { viewBox } = param;\n                                                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                    x: viewBox.cx,\n                                                                    y: viewBox.cy,\n                                                                    textAnchor: \"middle\",\n                                                                    dominantBaseline: \"middle\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            className: \"fill-foreground text-3xl font-bold\",\n                                                                            children: [\n                                                                                totalKpiAchievement,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: (viewBox.cy || 0) + 24,\n                                                                            className: \"fill-muted-foreground text-sm\",\n                                                                            children: \"Pencapaian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-col gap-2 text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 font-medium leading-none justify-center\",\n                                        children: dashboardData.kpis.achievement_percentage >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian melebihi target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : dashboardData.kpis.achievement_percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian mendekati target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian perlu ditingkatkan\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-gray-500 text-gray-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});