import React from 'react';
import { UserWithProfile } from '@/types/admin';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { CheckIcon, TrashIcon } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { DataTable, SortDirection } from '@/components/ui/data-table';

interface UserTableProps {
  users: UserWithProfile[];
  selectedUsers: string[];
  onSelectUser: (id: string, checked: boolean) => void;
  onSelectAll: (checked: boolean) => void;
  onActivate: (user: UserWithProfile) => void;
  onDelete: (user: UserWithProfile) => void;
  loading?: boolean;
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  selectedUsers,
  onSelectUser,
  onSelectAll,
  onActivate,
  onDelete,
  loading = false,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
  sortField,
  sortDirection,
  onSort,
}) => {
  // Define selection column
  const selectionColumn = {
    key: 'selection',
    header: (
      <Checkbox
        checked={users.length > 0 && selectedUsers.length === users.length}
        onCheckedChange={(checked) => onSelectAll(!!checked)}
      />
    ),
    width: '40px',
    render: (user: UserWithProfile) => {
      const isSelected = selectedUsers.includes(user.profile.id);
      return (
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) =>
            onSelectUser(user.profile.id, !!checked)
          }
        />
      );
    },
  };

  // Define data columns
  const dataColumns = [
    {
      key: 'fullname',
      header: 'NAMA',
      sortable: false,
      render: (user: UserWithProfile) => user.profile.fullname,
    },
    {
      key: 'email',
      header: 'EMAIL',
      sortable: false,
      render: (user: UserWithProfile) => user.email,
    },
    {
      key: 'role',
      header: 'ROLE',
      sortable: false,
      render: (user: UserWithProfile) => user.profile.role,
    },
    {
      key: 'created_at',
      header: 'TIMESTAMP',
      sortable: false,
      render: (user: UserWithProfile) =>
        formatDate(user.profile.created_at || ''),
    },
    {
      key: 'status',
      header: 'STATUS',
      sortable: false,
      render: (user: UserWithProfile) => (
        <Badge variant={user.profile.is_active ? 'success' : 'warning'}>
          {user.profile.is_active ? 'Terverifikasi' : 'Belum Terverifikasi'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      header: 'AKSI',
      width: '120px',
      align: 'center',
      render: (user: UserWithProfile) => (
        <div className="flex space-x-2 justify-center items-center">
          {!user.profile.is_active && (
            <Button
              onClick={() => onActivate(user)}
              variant="success"
              size="sm"
            >
              <CheckIcon className="h-4 w-4" />
            </Button>
          )}
          <Button
            onClick={() => onDelete(user)}
            variant="outline"
            size="sm"
            className="text-red-500 hover:text-red-600 hover:bg-red-50 border-red-300 hover:border-red-400"
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={[selectionColumn, ...dataColumns]}
      data={users}
      keyExtractor={(user) => user.profile.id}
      loading={loading}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage="Tidak ada data user ditemukan"
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
};

export default UserTable;
