// src/components/ui/navbar.tsx
'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Menu, UserCog, Loader2, LogOut } from 'lucide-react';
import useAuth from '@/hooks/auth/useAuth';

export function Navbar({ className }: { className?: string }) {
  const { user, signOut, isAuthenticated } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await signOut();
    // No need to set isLoggingOut to false as the page will redirect
  };

  const handleLogin = () => {
    router.push('/');
  };

  return (
    <header
      className={cn('w-full border-b bg-white shadow-sm py-4', className)}
    >
      <div className="flex justify-between h-8 px-6">
        {/* Logo in the left corner */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center">
            <Image
              src="/logo-navbar.svg"
              alt="Kasuat"
              width={160}
              height={40}
              className="h-8 w-auto"
            />
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
        >
          <Menu className="h-6 w-6" />
        </Button>

        {/* User info and logout */}
        <div className="hidden md:flex items-center space-x-4">
          {isAuthenticated && user ? (
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">{user.fullname}</span>
                {user.role && <Badge variant="secondary">{user.role}</Badge>}
              </div>
              <Link href="/update-account">
                <Button variant="outline">
                  <UserCog className="h-4 w-4 mr-2" />
                  Perbarui Akun
                </Button>
              </Link>
              <Button
                onClick={handleLogout}
                variant="outline"
                disabled={isLoggingOut}
                className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-400"
              >
                {isLoggingOut ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Keluar...
                  </>
                ) : (
                  <>
                    <LogOut className="h-4 w-4 mr-2" />
                    Keluar
                  </>
                )}
              </Button>
            </div>
          ) : (
            <Button onClick={handleLogin}>Login</Button>
          )}
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-[64px] right-0 left-0 bg-white shadow-md z-10 border-b">
            <div className="py-4 px-6 flex flex-col gap-4">
              {isAuthenticated && user ? (
                <>
                  <div className="flex flex-col gap-2">
                    <span className="text-sm font-medium text-gray-700">
                      {user.fullname}
                    </span>
                    {user.role && (
                      <Badge variant="secondary" className="w-fit">
                        {user.role}
                      </Badge>
                    )}
                  </div>
                  <Link href="/update-account">
                    <Button variant="outline" className="w-full">
                      <UserCog className="h-4 w-4 mr-2" />
                      Perbarui Akun
                    </Button>
                  </Link>
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    className="w-fit border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-400"
                    disabled={isLoggingOut}
                  >
                    {isLoggingOut ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Keluar...
                      </>
                    ) : (
                      <>
                        <LogOut className="h-4 w-4 mr-2" />
                        Keluar
                      </>
                    )}
                  </Button>
                </>
              ) : (
                <Button onClick={handleLogin}>Login</Button>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
